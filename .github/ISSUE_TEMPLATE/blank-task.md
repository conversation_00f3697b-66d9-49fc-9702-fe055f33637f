---
name: 📝 Blank Task
about: AI Agent general purpose task template
title: '[Task] [TASK_DESCRIPTION]'
labels: ['task', 'ai-agent']
assignees: ''
---

## 🎯 Task Description

**Objective:** <!-- What needs to be accomplished? -->
**Category:** <!-- Development/Design/Documentation/Other -->
**Priority:** <!-- High/Medium/Low -->
**Estimated Effort:** <!-- Small/Medium/Large -->

## 📋 Task Details

### Requirements
- [ ] <!-- Requirement 1 -->
- [ ] <!-- Requirement 2 -->
- [ ] <!-- Requirement 3 -->

### Acceptance Criteria
- [ ] <!-- Criteria 1 -->
- [ ] <!-- Criteria 2 -->
- [ ] <!-- Criteria 3 -->

### Constraints
- <!-- Any limitations or constraints -->

## 🛠️ AI Agent Instructions

### 1. Planning
- [ ] Understand task requirements
- [ ] Identify affected files/components
- [ ] Plan implementation approach
- [ ] Consider dependencies
- [ ] Estimate complexity

### 2. Implementation
- [ ] Follow project conventions
- [ ] Maintain code quality
- [ ] Ensure TypeScript compliance
- [ ] Add proper error handling
- [ ] Include necessary tests

### 3. Validation
- [ ] Test functionality works
- [ ] Verify requirements met
- [ ] Check for edge cases
- [ ] Ensure no regressions
- [ ] Validate performance

## 📁 Affected Areas

**Files:** <!-- List files that may be modified -->
**Components:** <!-- List components involved -->
**Dependencies:** <!-- Any new dependencies needed -->

## ✅ Completion Checklist

### Development
- [ ] Task implemented correctly
- [ ] Code follows project standards
- [ ] TypeScript types defined
- [ ] Error handling included
- [ ] Performance optimized

### Testing
- [ ] Functionality tested
- [ ] Edge cases covered
- [ ] No console errors
- [ ] Cross-browser compatibility
- [ ] Mobile responsiveness

### Documentation
- [ ] Code documented
- [ ] README updated if needed
- [ ] API docs updated if needed
- [ ] Comments added for complex logic

## 🚀 AI Agent Notes

- **Quality:** Maintain high code standards
- **Consistency:** Follow existing patterns
- **Testing:** Verify all functionality
- **Documentation:** Keep docs up to date
- **Communication:** Report any blockers or questions

## 📖 Additional Context

<!-- Add any additional information, links, or context that might be helpful -->

## 🔗 Related Issues

<!-- Link to related issues or PRs if applicable -->
