---
name: ⚡ Create Simple Vue Component
about: Quick AI Agent task for simple Vue components
title: '[Quick] Create [COMPONENT_NAME]'
labels: ['component', 'ai-agent', 'quick-task']
assignees: ''
---

## 🎯 Quick Component Task

**Component:** <!-- Component name -->
**Location:** <!-- app/components/[category]/ -->
**Type:** <!-- Base/UI/Feature -->

## 📝 Requirements

### Basic Info
- **Purpose:** <!-- What does this component do? -->
- **Props:** <!-- List main props -->
- **Style:** <!-- Tailwind classes, responsive -->

### Features
- [ ] TypeScript support
- [ ] Responsive design
- [ ] i18n for text
- [ ] Accessibility
- [ ] Theme support

## 🛠️ AI Instructions

1. **Create** component in correct directory
2. **Follow** existing project patterns
3. **Use** TypeScript and Tailwind CSS
4. **Add** i18n for user-facing text
5. **Test** component works correctly

## ✅ Checklist

- [ ] Component created and functional
- [ ] Follows project conventions
- [ ] TypeScript interfaces defined
- [ ] Responsive and accessible
- [ ] No console errors
- [ ] Ready for use

## 📋 Template

```vue
<template>
  <div class="component-wrapper">
    <!-- Component content -->
  </div>
</template>

<script setup lang="ts">
interface Props {
  // Define props
}

// Component logic
</script>
```

## 🚀 Notes

- Keep it simple and reusable
- Follow project naming conventions
- Use existing UI components when possible
- Optimize for performance
