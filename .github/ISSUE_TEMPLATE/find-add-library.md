---
name: 📚 Find & Add Library
about: AI Agent task to research and add suitable libraries
title: '[Library] Find and add library for [FUNCTIONALITY]'
labels: ['library', 'ai-agent', 'research', 'dependencies']
assignees: ''
---

## 📚 Library Requirements

**Functionality:** <!-- What functionality is needed? -->
**Use Case:** <!-- Specific use case or feature -->
**Priority:** <!-- High/Medium/Low -->
**Constraints:** <!-- Size/performance/compatibility constraints -->

## 🔍 Requirements Analysis

### Functional Requirements
- [ ] <!-- Requirement 1 -->
- [ ] <!-- Requirement 2 -->
- [ ] <!-- Requirement 3 -->

### Technical Requirements
- [ ] **Framework:** Vue 3 / Nuxt 3 compatible
- [ ] **TypeScript:** TypeScript support
- [ ] **SSR:** Server-side rendering support
- [ ] **Bundle Size:** Reasonable bundle impact
- [ ] **Performance:** Good performance characteristics

### Quality Requirements
- [ ] **Maintenance:** Actively maintained
- [ ] **Documentation:** Good documentation
- [ ] **Community:** Active community support
- [ ] **Testing:** Well tested
- [ ] **Security:** No known vulnerabilities

## 🛠️ AI Agent Instructions

### 1. Research Phase
- [ ] Search for suitable libraries
- [ ] Compare popular options
- [ ] Check compatibility with project
- [ ] Review documentation quality
- [ ] Analyze bundle size impact

### 2. Evaluation Criteria
- [ ] **Popularity:** GitHub stars, npm downloads
- [ ] **Maintenance:** Recent updates, active issues
- [ ] **Compatibility:** Vue 3, Nuxt 3, TypeScript
- [ ] **Performance:** Bundle size, runtime performance
- [ ] **API Quality:** Developer experience, ease of use

### 3. Selection Process
- [ ] Create comparison matrix
- [ ] Test top 2-3 candidates
- [ ] Evaluate integration complexity
- [ ] Check for conflicts with existing deps
- [ ] Make recommendation with rationale

### 4. Implementation
- [ ] Install chosen library
- [ ] Configure for project
- [ ] Create usage examples
- [ ] Update documentation
- [ ] Test integration

## 📊 Library Comparison

### Candidate Libraries
| Library | Stars | Downloads | Size | TS Support | Last Update |
|---------|-------|-----------|------|------------|-------------|
| <!-- Library 1 --> | | | | | |
| <!-- Library 2 --> | | | | | |
| <!-- Library 3 --> | | | | | |

### Evaluation Matrix
| Criteria | Weight | Lib 1 | Lib 2 | Lib 3 |
|----------|--------|-------|-------|-------|
| Functionality | 30% | | | |
| Performance | 25% | | | |
| Maintenance | 20% | | | |
| Documentation | 15% | | | |
| Compatibility | 10% | | | |

## 🔧 Integration Plan

### Installation
```bash
# Package manager command
npm install [library-name]
# or
pnpm add [library-name]
```

### Configuration
```typescript
// Configuration setup
// Nuxt module registration
// Plugin setup
```

### Usage Example
```vue
<template>
  <!-- Usage example -->
</template>

<script setup lang="ts">
// Import and usage
</script>
```

## 📋 Research Checklist

### Discovery
- [ ] Searched npm/GitHub for options
- [ ] Checked Vue/Nuxt ecosystem
- [ ] Reviewed community recommendations
- [ ] Analyzed existing solutions
- [ ] Considered building custom solution

### Evaluation
- [ ] Tested functionality
- [ ] Checked bundle size impact
- [ ] Verified TypeScript support
- [ ] Tested SSR compatibility
- [ ] Reviewed documentation

### Selection
- [ ] Compared top candidates
- [ ] Made informed decision
- [ ] Documented rationale
- [ ] Considered long-term maintenance
- [ ] Checked license compatibility

## ✅ Implementation Checklist

### Installation
- [ ] Library installed correctly
- [ ] Dependencies resolved
- [ ] No version conflicts
- [ ] Package.json updated
- [ ] Lock file updated

### Configuration
- [ ] Library configured properly
- [ ] Nuxt module registered if needed
- [ ] TypeScript types available
- [ ] SSR configuration done
- [ ] Environment setup complete

### Integration
- [ ] Created usage examples
- [ ] Updated project documentation
- [ ] Added to component library
- [ ] Tested in development
- [ ] Verified production build

### Testing
- [ ] Functionality works as expected
- [ ] No console errors/warnings
- [ ] Performance impact acceptable
- [ ] SSR works correctly
- [ ] TypeScript compilation successful

## 🚀 AI Agent Notes

- **Research Thoroughly:** Compare multiple options
- **Test Before Committing:** Try libraries before final selection
- **Consider Maintenance:** Choose actively maintained libraries
- **Bundle Size:** Monitor impact on application size
- **Documentation:** Prioritize well-documented libraries

## 📖 Recommended Sources

**NPM:** https://www.npmjs.com/
**GitHub:** https://github.com/
**Vue Ecosystem:** https://github.com/vuejs/awesome-vue
**Nuxt Modules:** https://nuxt.com/modules
**Bundle Analyzer:** Use to check size impact
