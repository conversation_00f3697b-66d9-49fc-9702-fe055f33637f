---
name: 🐛 Fix Bug
about: AI Agent task to fix bugs and issues
title: '[Bug] Fix [ISSUE_DESCRIPTION]'
labels: ['bug', 'ai-agent', 'fix']
assignees: ''
---

## 🐛 Bug Description

**Issue:** <!-- Brief description of the bug -->
**Location:** <!-- File/component/page where bug occurs -->
**Severity:** <!-- Critical/High/Medium/Low -->
**Environment:** <!-- Browser/Device/OS if relevant -->

## 🔍 Bug Details

### Current Behavior
<!-- What is happening now? -->

### Expected Behavior
<!-- What should happen instead? -->

### Steps to Reproduce
1. <!-- Step 1 -->
2. <!-- Step 2 -->
3. <!-- Step 3 -->

### Error Messages
```
<!-- Console errors, stack traces, etc. -->
```

## 🛠️ AI Agent Instructions

### 1. Investigation
- [ ] Reproduce the bug locally
- [ ] Identify root cause
- [ ] Check related components/files
- [ ] Review recent changes
- [ ] Analyze error logs/console

### 2. Analysis
- [ ] Determine scope of impact
- [ ] Check for similar issues
- [ ] Identify potential side effects
- [ ] Plan minimal fix approach

### 3. Fix Implementation
- [ ] Implement targeted fix
- [ ] Preserve existing functionality
- [ ] Follow project conventions
- [ ] Add error handling if needed
- [ ] Update TypeScript types if needed

### 4. Testing
- [ ] Test fix works correctly
- [ ] Test edge cases
- [ ] Verify no regression
- [ ] Test on different devices/browsers
- [ ] Check console for new errors

## 📋 Fix Checklist

### Code Quality
- [ ] Minimal, targeted changes
- [ ] Follows project patterns
- [ ] Proper error handling
- [ ] TypeScript compliance
- [ ] No console warnings

### Functionality
- [ ] Bug is resolved
- [ ] No new issues introduced
- [ ] All features work as expected
- [ ] Performance not degraded
- [ ] Accessibility maintained

### Documentation
- [ ] Code comments added if complex
- [ ] Update relevant documentation
- [ ] Add JSDoc if new functions

## 🚀 AI Agent Notes

- **Focus:** Fix only the specific issue
- **Caution:** Avoid over-engineering
- **Testing:** Thorough verification required
- **Impact:** Consider all affected areas
- **Rollback:** Ensure changes are reversible
