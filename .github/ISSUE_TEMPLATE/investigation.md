---
name: 🔍 Investigation Task
about: AI Agent task to investigate issues, analyze code, or research solutions
title: '[Investigation] [TOPIC_TO_INVESTIGATE]'
labels: ['investigation', 'ai-agent', 'research']
assignees: ''
---

## 🔍 Investigation Scope

**Topic:** <!-- What needs to be investigated? -->
**Area:** <!-- Codebase area, feature, or system -->
**Priority:** <!-- High/Medium/Low -->
**Deadline:** <!-- When is this needed? -->

## 📋 Investigation Goals

### Primary Objectives
- [ ] <!-- Main goal 1 -->
- [ ] <!-- Main goal 2 -->
- [ ] <!-- Main goal 3 -->

### Questions to Answer
1. <!-- Question 1 -->
2. <!-- Question 2 -->
3. <!-- Question 3 -->

## 🛠️ AI Agent Instructions

### 1. Code Analysis
- [ ] Scan relevant files and directories
- [ ] Analyze code patterns and structures
- [ ] Identify dependencies and relationships
- [ ] Review recent changes/commits
- [ ] Check for similar implementations

### 2. Documentation Review
- [ ] Read existing documentation
- [ ] Check README files
- [ ] Review API documentation
- [ ] Analyze configuration files
- [ ] Study package.json dependencies

### 3. Testing & Validation
- [ ] Run existing tests
- [ ] Test current functionality
- [ ] Identify edge cases
- [ ] Check performance metrics
- [ ] Validate assumptions

### 4. Research
- [ ] Search for best practices
- [ ] Review similar projects
- [ ] Check library documentation
- [ ] Analyze community solutions
- [ ] Study official guidelines

## 📊 Expected Deliverables

### Analysis Report
- [ ] **Summary:** Key findings overview
- [ ] **Details:** In-depth analysis
- [ ] **Issues:** Problems identified
- [ ] **Opportunities:** Improvement areas
- [ ] **Recommendations:** Suggested actions

### Code Examples
```typescript
// Include relevant code snippets
// Show current implementation
// Demonstrate issues or patterns
```

### Documentation
- [ ] Findings summary
- [ ] Technical details
- [ ] Recommendations
- [ ] Next steps
- [ ] References and links

## 🎯 Investigation Areas

### Code Quality
- [ ] Architecture patterns
- [ ] Code organization
- [ ] Performance bottlenecks
- [ ] Security concerns
- [ ] Maintainability issues

### Functionality
- [ ] Feature completeness
- [ ] User experience
- [ ] Error handling
- [ ] Edge cases
- [ ] Integration points

### Technical Debt
- [ ] Outdated dependencies
- [ ] Deprecated patterns
- [ ] Missing tests
- [ ] Documentation gaps
- [ ] Refactoring opportunities

## 📋 Investigation Checklist

### Preparation
- [ ] Understand investigation scope
- [ ] Identify key areas to examine
- [ ] Gather necessary tools/access
- [ ] Plan investigation approach

### Execution
- [ ] Systematic code review
- [ ] Document findings
- [ ] Test hypotheses
- [ ] Validate conclusions
- [ ] Organize results

### Reporting
- [ ] Clear summary of findings
- [ ] Actionable recommendations
- [ ] Supporting evidence
- [ ] Risk assessment
- [ ] Implementation roadmap

## 🚀 AI Agent Notes

- **Thoroughness:** Be comprehensive but focused
- **Documentation:** Record all findings clearly
- **Objectivity:** Present facts and evidence
- **Actionability:** Provide concrete next steps
- **Context:** Consider project constraints and goals
