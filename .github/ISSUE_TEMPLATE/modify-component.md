---
name: 🔧 Modify Component
about: AI Agent task to modify existing Vue components
title: '[Modify] Update [COMPONENT_NAME] - [CHANGE_DESCRIPTION]'
labels: ['component', 'ai-agent', 'modification']
assignees: ''
---

## 🔧 Component Modification

**Component:** <!-- Component name and path -->
**Change Type:** <!-- Feature/Enhancement/Refactor/Style -->
**Priority:** <!-- High/Medium/Low -->
**Impact:** <!-- Low/Medium/High -->

## 📋 Modification Details

### Current State
<!-- Describe current component behavior/appearance -->

### Desired Changes
- [ ] <!-- Change 1 -->
- [ ] <!-- Change 2 -->
- [ ] <!-- Change 3 -->

### Requirements
- [ ] Maintain existing functionality
- [ ] Preserve component API (props/events)
- [ ] Follow project conventions
- [ ] Ensure backward compatibility

## 🛠️ AI Agent Instructions

### 1. Analysis
- [ ] Review current component code
- [ ] Understand existing functionality
- [ ] Identify dependencies and usage
- [ ] Check component props and events
- [ ] Analyze current styling/layout

### 2. Planning
- [ ] Plan modification approach
- [ ] Identify files to change
- [ ] Consider impact on parent components
- [ ] Plan testing strategy
- [ ] Check for breaking changes

### 3. Implementation
- [ ] Make targeted modifications
- [ ] Preserve existing API
- [ ] Update TypeScript interfaces
- [ ] Maintain responsive design
- [ ] Follow accessibility guidelines

### 4. Testing
- [ ] Test modified functionality
- [ ] Verify existing features work
- [ ] Test component in different contexts
- [ ] Check responsive behavior
- [ ] Validate accessibility

## 📁 Component Information

**File Path:** <!-- app/components/[category]/ComponentName.vue -->
**Category:** <!-- base/ai-tool/layout/etc -->
**Dependencies:** <!-- Other components it uses -->
**Used By:** <!-- Components/pages that use this -->

## 🎨 Modification Types

### UI/UX Changes
- [ ] **Styling:** Update appearance/layout
- [ ] **Animation:** Add/modify animations
- [ ] **Responsive:** Improve mobile experience
- [ ] **Accessibility:** Enhance a11y features
- [ ] **Theme:** Update dark/light mode support

### Functionality Changes
- [ ] **Props:** Add/modify component props
- [ ] **Events:** Add/modify emitted events
- [ ] **Logic:** Update component behavior
- [ ] **Performance:** Optimize rendering
- [ ] **Error Handling:** Improve error states

### Technical Changes
- [ ] **TypeScript:** Update type definitions
- [ ] **Composables:** Add/update composables
- [ ] **i18n:** Add/update internationalization
- [ ] **Dependencies:** Update imports/dependencies
- [ ] **Structure:** Refactor component structure

## ✅ Modification Checklist

### Code Quality
- [ ] Changes follow project patterns
- [ ] TypeScript types updated
- [ ] Code is clean and readable
- [ ] No console warnings/errors
- [ ] Performance not degraded

### Functionality
- [ ] New features work correctly
- [ ] Existing features preserved
- [ ] Component API maintained
- [ ] Props/events work as expected
- [ ] Error handling improved

### Design
- [ ] UI changes look good
- [ ] Responsive on all devices
- [ ] Animations smooth and appropriate
- [ ] Accessibility maintained/improved
- [ ] Theme support works

### Integration
- [ ] Component works in existing contexts
- [ ] No breaking changes for consumers
- [ ] Documentation updated if needed
- [ ] Examples updated if needed

## 🚀 AI Agent Notes

- **Caution:** Preserve existing functionality
- **Testing:** Thoroughly test all changes
- **Compatibility:** Ensure backward compatibility
- **Documentation:** Update if API changes
- **Performance:** Monitor impact on performance

## 📖 Reference

**Current Usage:** <!-- How component is currently used -->
**Design System:** <!-- Follow project design patterns -->
**Similar Components:** <!-- Reference similar components for consistency -->
