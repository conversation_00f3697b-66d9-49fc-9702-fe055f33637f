name: PR Preview

on:
  pull_request:
    branches: [ develop ]
    types: [opened, synchronize, reopened]

jobs:
  build-and-deploy:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      pull-requests: write

    steps:
    - name: Checkout
      uses: actions/checkout@v4

    - name: Setup Node.js
      uses: actions/setup-node@v4
      with:
        node-version: '20'

    - name: Setup pnpm
      uses: pnpm/action-setup@v4
      with:
        version: '10.10.0'

    - name: Get pnpm store directory
      shell: bash
      run: |
        echo "STORE_PATH=$(pnpm store path --silent)" >> $GITHUB_ENV

    - name: Setup pnpm cache
      uses: actions/cache@v4
      with:
        path: ${{ env.STORE_PATH }}
        key: ${{ runner.os }}-pnpm-store-${{ hashFiles('**/pnpm-lock.yaml') }}
        restore-keys: |
          ${{ runner.os }}-pnpm-store-

    - name: Install dependencies
      run: pnpm install --frozen-lockfile

    - name: Build project
      run: pnpm run generate
      env:
        NUXT_UI_PRO_LICENSE: ${{ secrets.NUXT_UI_PRO_LICENSE }}

    - name: Deploy to GitHub Pages (PR Preview)
      uses: peaceiris/actions-gh-pages@v4
      with:
        personal_token: ${{ secrets.NUXT_UI_GITHUB_TOKEN }}
        publish_dir: ./.output/public
        destination_dir: pr-${{ github.event.number }}
        keep_files: true

    - name: Comment PR
      uses: actions/github-script@v7
      with:
        script: |
          const prNumber = context.payload.pull_request.number;
          const previewUrl = `https://${{ github.repository_owner }}.github.io/${{ github.event.repository.name }}/pr-${prNumber}/`;

          // Find existing comment
          const comments = await github.rest.issues.listComments({
            owner: context.repo.owner,
            repo: context.repo.repo,
            issue_number: prNumber
          });

          const botComment = comments.data.find(comment =>
            comment.user.type === 'Bot' &&
            comment.body.includes('🚀 Preview deployed')
          );

          const body = `🚀 Preview deployed successfully!

          **Preview URL**: ${previewUrl}

          This preview will be updated automatically when you push new commits to this PR.

          ---
          *Powered by GitHub Actions PR Preview*`;

          if (botComment) {
            await github.rest.issues.updateComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              comment_id: botComment.id,
              body: body
            });
          } else {
            await github.rest.issues.createComment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              issue_number: prNumber,
              body: body
            });
          }
