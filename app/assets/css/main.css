@import "tailwindcss";
@import "@nuxt/ui-pro";
@import "./light-mode.css";

@source "../../../content/**/*";

@theme static {
  --font-sans: 'Be Vietnam Pro', 'Public Sans', sans-serif;
  --font-serif: 'Instrument Serif', serif;
}

:root {
  --ui-container: var(--container-8xl);
  font-family: 'Be Vietnam Pro', 'Public Sans', sans-serif;
  background-color: #f5f5f7;
  color: #333333;

  ::selection {
    color: #282a30;
    background-color: #c8c8c8;
  }
}

.dark {
  background-color: initial;
  color: initial;

  ::selection {
    color: #ffffff;
    background-color: #474747;
  }
}


.logo-container {
  position: relative;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 2rem;
}

#main-logo {
  transition: all 0.8s cubic-bezier(0.34, 1.56, 0.64, 1);
  z-index: 30;
  transform-origin: center center;
  will-change: transform, top, left, position;
}

#main-logo.logo-loading {
  position: fixed;
  top: 50vh;
  left: 50vw;
  transform: translate(-50%, -50%) scale(1.05);
  z-index: 10000;
  animation: pulse 2s infinite ease-in-out;
  /* Ensure logo is visible */
  opacity: 1 !important;
  visibility: visible !important;
  /* Add shadow for better visibility */
  filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.2));
}

:root:not(.dark) #main-logo.logo-loading {
  /* Lighter shadow for better visibility in light mode */
  filter: drop-shadow(0 0 20px rgba(0, 0, 0, 0.15));
  animation: pulse-light 2s infinite ease-in-out;
}

@keyframes pulse {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1.05);
    filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.2));
  }

  50% {
    transform: translate(-50%, -50%) scale(1.1);
    filter: drop-shadow(0 0 30px rgba(255, 255, 255, 0.3));
  }
}

@keyframes pulse-light {

  0%,
  100% {
    transform: translate(-50%, -50%) scale(1.05);
    filter: drop-shadow(0 0 20px rgba(0, 0, 0, 0.15));
  }

  50% {
    transform: translate(-50%, -50%) scale(1.1);
    filter: drop-shadow(0 0 30px rgba(0, 0, 0, 0.2));
  }
}

.masonry {
  column-count: 3;
  column-gap: 1rem;
}

.masonry-item {
  break-inside: avoid;
  margin-bottom: 1rem;
}

@media (max-width: 1024px) {
  .masonry {
    column-count: 2;
  }
}
@media (max-width: 768px) {
  .masonry {
    column-count: 1;
  }
}

.hide-scrollbar {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

.thin-scrollbar {
  scrollbar-width: thin;
  scrollbar-color: rgba(0, 0, 0, 0.2) transparent;
}

.thin-scrollbar::-webkit-scrollbar {
  width: 8px;
}

.thin-scrollbar::-webkit-scrollbar-track {
  background: transparent;
}

.thin-scrollbar::-webkit-scrollbar-thumb {
  background-color: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  border: 3px solid transparent;
  background-clip: content-box;
}
