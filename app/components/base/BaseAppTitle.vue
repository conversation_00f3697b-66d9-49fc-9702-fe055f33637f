<script setup lang="ts">
// Using $t() directly in the template
</script>

<template>
  <!-- <div class="tilt-wrapper">
    <div class="hero w-fit mx-auto">
      <div
        class="glow-text lg:text-2xl text-xl transition-all duration-300 text-primary-800 dark:text-white dark:opacity-75 opacity-100"
        :data-text="$t('appName')"
      >
        {{ $t('appName') }}
      </div>
    </div>
  </div> -->
  <h1
    :data-shadow="$t('appName')"
    class="dark:text-white text-primary"
  >
    {{ $t("appName") }}
  </h1>
</template>

<style lang="css" scoped>
@import url(https://fonts.googleapis.com/css?family=Righteous);

*,
*:before,
*:after {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  position: relative;
}

body:before {
  content: "";
  display: inline-block;
  vertical-align: middle;
  font-size: 0;
  height: 100%;
}

h1 {
  display: inline-block;
  font-family: "Righteous", serif;
  font-size: 12em;
  text-shadow: 0.03em 0.03em 0 hsla(230, 40%, 50%, 1);
}
h1:after {
  content: attr(data-shadow);
  position: absolute;
  top: 0.06em;
  left: 0.06em;
  z-index: -1;
  text-shadow: none;
  background-image: linear-gradient(
    45deg,
    transparent 45%,
    hsla(48, 20%, 90%, 1) 45%,
    hsla(48, 20%, 90%, 1) 55%,
    transparent 0
  );
  background-size: 0.05em 0.05em;
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;

  animation: shad-anim 15s linear infinite;
}

@keyframes shad-anim {
  0% {
    background-position: 0 0;
  }
  0% {
    background-position: 100% -100%;
  }
}
</style>
