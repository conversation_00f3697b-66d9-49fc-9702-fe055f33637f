<script setup lang="ts">
interface HistoryFilterSelectProps {
  modelValue: string
}

const props = defineProps<HistoryFilterSelectProps>()
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const { t } = useI18n()

// Filter options for history
const historyFilterOptions = [
  {
    label: t('historyFilter.all'),
    value: 'all',
    icon: 'material-symbols:border-all-rounded'
  },
  {
    label: t('Image'),
    value: 'image',
    icon: 'hugeicons:ai-image'
  },
  {
    label: t('Video'),
    value: 'video',
    icon: 'hugeicons:ai-video'
  },
  {
    label: t('Text to Speech'),
    value: 'tts-text',
    icon: 'hugeicons:ai-voice'
  },
  {
    label: t('Document to Speech'),
    value: 'tts-document',
    icon: 'basil:document-outline'
  },
  {
    label: t('Dialogue'),
    value: 'tts-multi-speaker',
    icon: 'ri:chat-smile-ai-line'
  }
]

const updateValue = (value: string | number) => {
  emit('update:modelValue', String(value))
}
</script>

<template>
  <UTabs
    :model-value="props.modelValue"
    :items="historyFilterOptions"
    class="w-full"
    v-bind="$attrs"
    @update:model-value="updateValue"
  />
</template>
