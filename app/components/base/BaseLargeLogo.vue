<script lang="ts" setup>
const props = defineProps({
  loading: {
    type: Boolean,
    default: false
  },
  size: {
    type: String,
    default: 'large', // 'small', 'medium', 'large', 'xlarge'
    validator: (value: string) => ['small', 'medium', 'large', 'xlarge'].includes(value)
  }
})

// Calculate size multiplier and CSS classes
const sizeConfig = computed(() => {
  switch (props.size) {
    case 'small':
      return {
        multiplier: 1,
        circleClass: 'w-9 h-9',
        loadingSize: '3rem'
      }
    case 'medium':
      return {
        multiplier: 2,
        circleClass: 'w-20 h-20', // 80px
        loadingSize: '14rem'
      }
    case 'large':
      return {
        multiplier: 4,
        circleClass: 'w-40 h-40', // 160px
        loadingSize: '28rem'
      }
    case 'xlarge':
      return {
        multiplier: 6,
        circleClass: 'w-60 h-60', // 240px
        loadingSize: '42rem'
      }
    default:
      return {
        multiplier: 4,
        circleClass: 'w-40 h-40',
        loadingSize: '28rem'
      }
  }
})
</script>

<template>
  <div
    class="circulate"
    :class="{ 'loading-active': loading }"
  >
    <div
      class="circle border dark:border-gray-700"
      :class="[sizeConfig.circleClass, { 'loading-rotate': loading }]"
    >
      <div
        class="wave _one"
        :class="{ 'loading-wave': loading }"
      />
      <div
        class="wave _two"
        :class="{ 'loading-wave': loading }"
      />
      <div
        class="wave _three"
        :class="{ 'loading-wave': loading }"
      />
      <div
        class="reflection"
        :class="{ 'loading-reflection': loading }"
      />

      <!-- Image elements being processed inside the ball -->
      <div
        class="image-element photo photo1"
        :class="{ 'loading-photo': loading }"
      />
      <div
        class="image-element photo photo2"
        :class="{ 'loading-photo': loading }"
      />
      <div
        class="image-element photo photo3"
        :class="{ 'loading-photo': loading }"
      />
      <div
        class="image-element photo photo4"
        :class="{ 'loading-photo': loading }"
      />

      <!-- Particles to enhance the processing effect -->
      <div
        class="particles"
        :class="{ 'loading-particles': loading }"
      >
        <div
          v-for="n in 8"
          :key="n"
          class="particle"
          :class="[`p${n}`, { 'loading-particle': loading }]"
        />
      </div>

      <!-- Energy glow effect -->
      <div
        class="energy-glow"
        :class="{ 'loading-glow': loading }"
      />
    </div>
  </div>
</template>

<style lang="scss" scoped>
/* Dynamic size variables */
.circulate {
  --size-multiplier: v-bind('sizeConfig.multiplier');
  --loading-size: v-bind('sizeConfig.loadingSize');
}
/* Define the gradient angle custom property with Safari fallback */
@property --gradient-angle {
  syntax: "<angle>";
  initial-value: 0turn;
  inherits: false;
}

/* Safari-specific fixes for iOS mobile */
@supports (-webkit-appearance: none) {
  .safari-ios-fix {
    /* Fallback for Safari that doesn't support CSS properties */
    --gradient-angle: 0deg;
  }
}

.circulate {
  position: relative;
  z-index: 10;
  transition: all 0.5s ease-in-out;
  width: auto;
  height: auto;
  display: flex;
  justify-content: center;
  align-items: center;
  background: transparent; /* Đảm bảo nền trong suốt */
  /* Safari-specific fixes */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

/* Loading state styles for the container */
.circulate.loading-active {
  width: 100%;
  height: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  margin: 0;
  padding: 0;
  z-index: 10001;
  transform: none;
  background: transparent; /* Đảm bảo nền trong suốt */
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
}

.circle {
  border-radius: 100% !important;
}

.circulate .circle {
  border-radius: 100% !important;
  background: white;
  /* Enhanced 3D shadow for crystal ball effect - scaled up */
  box-shadow:
    /* Main shadow */
    0 30px 80px rgba(0, 0, 0, 0.4),
    0 60px 160px rgba(0, 0, 0, 0.3),
    /* Colored glow */
    0 0 300px rgba(142, 45, 226, 0.3),
    /* Inner highlights */
    inset 0 0 60px rgba(255, 255, 255, 0.4),
    inset -20px -20px 40px rgba(0, 0, 0, 0.2),
    inset 20px 20px 40px rgba(255, 255, 255, 0.2),
    /* Depth shadow */
    0 0 0 8px rgba(255, 255, 255, 0.1),
    0 0 0 16px rgba(142, 45, 226, 0.1);
  background: linear-gradient(
    135deg,
    rgba(35, 52, 93, 0.95),
    rgba(146, 22, 100, 0.95),
    rgba(35, 52, 93, 0.9)
  );
  position: relative;
  overflow: hidden;
  /* Enhanced 3D transform */
  transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  /* Safari-specific fixes for black square issue */
  -webkit-transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  -webkit-backface-visibility: hidden;
  backface-visibility: hidden;
  /* Prevent Safari rendering artifacts */
  isolation: isolate;
  /* Add thin border for more definition */
  border: 1px solid rgba(255, 255, 255, 0.15);
}

/* Adjust for light mode with Safari compatibility */
:root:not(.dark) .circulate .circle {
  box-shadow:
    /* Main shadow */
    0 25px 70px rgba(0, 0, 0, 0.3),
    0 50px 140px rgba(0, 0, 0, 0.2),
    /* Colored glow */
    0 0 250px rgba(142, 45, 226, 0.2),
    /* Inner highlights */
    inset 0 0 50px rgba(255, 255, 255, 0.5),
    inset -15px -15px 30px rgba(0, 0, 0, 0.15),
    inset 15px 15px 30px rgba(255, 255, 255, 0.3),
    /* Depth shadow */
    0 0 0 6px rgba(255, 255, 255, 0.15),
    0 0 0 12px rgba(142, 45, 226, 0.1);
  background: linear-gradient(
    135deg,
    rgba(35, 52, 93, 0.85),
    rgba(146, 22, 100, 0.85),
    rgba(35, 52, 93, 0.8)
  );
  /* Glass effect with Safari fallbacks */
  backdrop-filter: blur(15px);
  -webkit-backdrop-filter: blur(15px);
  /* Enhanced 3D transform - no hover animation */
  -webkit-transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  transform: perspective(2000px) rotateX(10deg) rotateY(-5deg);
  /* Remove transitions */
  transition: none;
  /* Enhanced thin border */
  border: 1px solid rgba(255, 255, 255, 0.25);

  &::before {
    content: "";
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: radial-gradient(
      ellipse at 25% 25%,
      rgba(255, 255, 255, 0.4) 0%,
      rgba(255, 255, 255, 0.2) 30%,
      rgba(255, 255, 255, 0.1) 50%,
      transparent 70%
    );
    border-radius: 100%;
    z-index: 2;
    pointer-events: none;
    /* Safari-specific fixes */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Add secondary highlight */
  &::after {
    content: "";
    position: absolute;
    top: 10%;
    left: 10%;
    width: 30%;
    height: 30%;
    background: radial-gradient(
      circle at center,
      rgba(255, 255, 255, 0.6) 0%,
      rgba(255, 255, 255, 0.3) 40%,
      transparent 70%
    );
    border-radius: 100%;
    z-index: 3;
    pointer-events: none;
    filter: blur(1px);
    /* Safari-specific fixes */
    -webkit-transform: translateZ(0);
    transform: translateZ(0);
  }

  /* Thin gradient border animation when loading with Safari compatibility */
  &.loading-rotate {
    animation: 2s gradient-angle infinite linear;
    border: 1px solid transparent;
    /* Simplified background layers for Safari */
    background: linear-gradient(
      135deg,
      rgba(35, 52, 93, 0.9),
      rgba(146, 22, 100, 0.9)
    );
    /* Fallback border animation for Safari */
    /* Increase size slightly when loading - dynamic */
    width: var(--loading-size) !important;
    height: var(--loading-size) !important;
    -webkit-transform: perspective(1600px) rotateX(5deg) scale(1.1);
    transform: perspective(1600px) rotateX(5deg) scale(1.1);
  }
}

/* Safari-specific thin gradient border fallback */
@supports not (background-clip: border-box) {
  :root:not(.dark) .circulate .circle.loading-rotate {
    border: 1px solid #00cfff;
    animation: border-color-cycle 2s infinite linear;
  }
}

@keyframes border-color-cycle {
  0% {
    border-color: #00cfff;
  }
  25% {
    border-color: #a600ff;
  }
  50% {
    border-color: #ff006e;
  }
  75% {
    border-color: #ff8800;
  }
  100% {
    border-color: #00cfff;
  }
}

.circulate .wave {
  opacity: 0.6;
  position: absolute;
  top: 3%;
  left: 50%;
  background: rgba(15, 16, 35, 0.7);
  width: calc(200px * var(--size-multiplier));
  height: calc(200px * var(--size-multiplier));
  margin-left: calc(-100px * var(--size-multiplier));
  margin-top: calc(-155px * var(--size-multiplier));
  transform-origin: 50% 48%;
  border-radius: 43%;
  /* Restore animation */
  animation: humming 2.5s infinite linear;
  /* Sharper waves with dynamic blur */
  filter: blur(calc(1px * var(--size-multiplier)));
  /* Add 3D depth */
  box-shadow: inset 0 0 calc(5px * var(--size-multiplier)) rgba(0, 0, 0, 0.3);
}

.circulate .wave._three {
  animation: humming 5000ms infinite linear;
  opacity: 0.7;
  filter: blur(calc(2px * var(--size-multiplier)));
  background: rgba(15, 16, 35, 0.8);
}

.circulate .wave._two {
  animation: humming 9000ms infinite linear;
  opacity: 0.9;
  background: rgba(15, 16, 35, 0.9);
  filter: blur(calc(0.5px * var(--size-multiplier)));
}

/* Loading state - add rotation animation only when loading */
.circulate .wave.loading-wave {
  opacity: 0.8;
  animation: humming 2s infinite linear;
}

.circulate .wave._three.loading-wave {
  opacity: 0.9;
  animation: humming 3s infinite linear;
}

.circulate .wave._two.loading-wave {
  opacity: 1;
  animation: humming 4s infinite linear;
}

.circulate .box:after {
  content: "";
  display: block;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 11;
  transform: translate3d(0, 0, 0);
}

/* Light reflection effect - dynamic size with animation */
.circulate .reflection {
  position: absolute;
  width: 35%;
  height: 20%;
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.4) 0%,
    rgba(255, 255, 255, 0.2) 50%,
    transparent 100%
  );
  border-radius: 50%;
  top: 12%;
  left: 12%;
  transform: rotate(-35deg);
  filter: blur(calc(2px * var(--size-multiplier)));
  z-index: 4;
  /* Add 3D depth with dynamic size */
  box-shadow: 0 0 calc(8px * var(--size-multiplier)) rgba(255, 255, 255, 0.3);
  /* Restore animation */
  animation: moveReflection 8s infinite ease-in-out;
}

/* Enhanced reflection when loading */
.circulate .reflection.loading-reflection {
  background: linear-gradient(
    135deg,
    rgba(255, 255, 255, 0.6) 0%,
    rgba(255, 255, 255, 0.3) 50%,
    transparent 100%
  );
  filter: blur(calc(1.5px * var(--size-multiplier)));
  box-shadow: 0 0 calc(10px * var(--size-multiplier)) rgba(255, 255, 255, 0.4);
  animation-duration: 4s;
}

.circulate .title {
  position: absolute;
  left: 0;
  top: 80px; /* 20px * 4 */
  width: 100%;
  z-index: 1;
  line-height: 120px; /* 30px * 4 */
  text-align: center;
  transform: translate3d(0, 0, 0);
  color: white;
  text-transform: uppercase;
  font-family: "Playfair Display", serif;
  letter-spacing: 1.6em; /* 0.4em * 4 */
  font-size: 96px; /* 24px * 4 */
  text-shadow: 0 4px 0 rgba(0, 0, 0, 0.1); /* 0 1px * 4 */
  text-indent: 1.2em; /* 0.3em * 4 */
}

@keyframes humming {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes moveReflection {
  0%,
  100% {
    opacity: 0.7;
    transform: rotate(-40deg) translateY(0);
  }
  50% {
    opacity: 0.9;
    transform: rotate(-35deg) translateY(20px); /* 5px * 4 */
  }
}

/* Image elements styles - dynamic size and sharp */
.circulate .image-element {
  position: absolute;
  background-color: rgba(255, 255, 255, 0.8);
  box-shadow:
    0 0 calc(8px * var(--size-multiplier)) rgba(255, 255, 255, 0.9),
    0 calc(2px * var(--size-multiplier)) calc(4px * var(--size-multiplier)) rgba(0, 0, 0, 0.3),
    inset 0 calc(1px * var(--size-multiplier)) calc(2px * var(--size-multiplier)) rgba(255, 255, 255, 0.5);
  z-index: 5;
  opacity: 0.9;
  /* Keep sharp for clarity */
  filter: blur(calc(0.5px * var(--size-multiplier)));
  /* Add 3D transform */
  transform-style: preserve-3d;
}

.circulate .image-element.photo {
  width: calc(8px * var(--size-multiplier));
  height: calc(8px * var(--size-multiplier));
  border-radius: calc(2px * var(--size-multiplier));
  background-size: cover;
  background-position: center;
  box-shadow:
    0 0 calc(8px * var(--size-multiplier)) rgba(255, 255, 255, 0.8),
    0 calc(2px * var(--size-multiplier)) calc(4px * var(--size-multiplier)) rgba(0, 0, 0, 0.4),
    inset 0 calc(1px * var(--size-multiplier)) calc(2px * var(--size-multiplier)) rgba(255, 255, 255, 0.6),
    inset 0 calc(-1px * var(--size-multiplier)) calc(2px * var(--size-multiplier)) rgba(0, 0, 0, 0.2);
  opacity: 0.95;
  /* Add thin border for definition */
  border: calc(0.25px * var(--size-multiplier)) solid rgba(255, 255, 255, 0.2);
}

.circulate .image-element.photo::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    45deg,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 70%
  );
  z-index: 3;
}

.circulate .image-element.photo1 {
  top: 30%;
  left: 25%;
  background: linear-gradient(
    45deg,
    #f5a623 25%,
    #f8e71c 50%,
    #f5a623 75%
  );
  transform: scale(0.8) rotate(-5deg);
  animation: float 7s infinite ease-in-out, rotate 15s infinite linear,
    pulse 3s infinite alternate;
}

.circulate .image-element.photo2 {
  width: calc(6px * var(--size-multiplier));
  height: calc(9px * var(--size-multiplier));
  top: 60%;
  left: 60%;
  background: linear-gradient(
    45deg,
    #7ed321 25%,
    #b8e986 50%,
    #7ed321 75%
  );
  transform: scale(0.7) rotate(10deg);
  animation: float 8s infinite ease-in-out, rotate 12s infinite linear reverse,
    pulse 4s infinite alternate;
  animation-delay: 0.5s;
}

.circulate .image-element.photo3 {
  width: calc(7px * var(--size-multiplier));
  height: calc(7px * var(--size-multiplier));
  top: 40%;
  left: 55%;
  background: linear-gradient(
    45deg,
    #bd10e0 25%,
    #d86eff 50%,
    #bd10e0 75%
  );
  transform: scale(0.75) rotate(-8deg);
  animation: float 6s infinite ease-in-out, rotate 18s infinite linear,
    pulse 5s infinite alternate;
  animation-delay: 1s;
}

.circulate .image-element.photo4 {
  width: calc(16px * var(--size-multiplier));
  height: calc(12px * var(--size-multiplier));
  top: 25%;
  left: 45%;
  background: linear-gradient(
    45deg,
    #4a90e2 25%,
    #50e3c2 50%,
    #4a90e2 75%
  );
  transform: scale(0.65) rotate(15deg);
  animation: float 9s infinite ease-in-out, rotate 20s infinite linear reverse,
    pulse 6s infinite alternate;
  animation-delay: 1.5s;
}

/* Particles styles - scaled up */
.circulate .particles {
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  z-index: 1;
}

.circulate .particle {
  position: absolute;
  width: calc(3px * var(--size-multiplier));
  height: calc(3px * var(--size-multiplier));
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 50%;
  filter: blur(calc(1px * var(--size-multiplier)));
}

.circulate .particle.p1 {
  top: 20%;
  left: 30%;
  animation: particleFloat 4s infinite ease-in-out;
}
.circulate .particle.p2 {
  top: 70%;
  left: 40%;
  animation: particleFloat 5s infinite ease-in-out;
  animation-delay: 0.3s;
}
.circulate .particle.p3 {
  top: 40%;
  left: 70%;
  animation: particleFloat 6s infinite ease-in-out;
  animation-delay: 0.6s;
}
.circulate .particle.p4 {
  top: 60%;
  left: 20%;
  animation: particleFloat 7s infinite ease-in-out;
  animation-delay: 0.9s;
}
.circulate .particle.p5 {
  top: 30%;
  left: 60%;
  animation: particleFloat 5s infinite ease-in-out;
  animation-delay: 1.2s;
}
.circulate .particle.p6 {
  top: 50%;
  left: 50%;
  animation: particleFloat 6s infinite ease-in-out;
  animation-delay: 1.5s;
}
.circulate .particle.p7 {
  top: 75%;
  left: 65%;
  animation: particleFloat 7s infinite ease-in-out;
  animation-delay: 1.8s;
}
.circulate .particle.p8 {
  top: 35%;
  left: 35%;
  animation: particleFloat 4s infinite ease-in-out;
  animation-delay: 2.1s;
}

/* Energy glow effect with dynamic size and animation */
.circulate .energy-glow {
  position: absolute;
  width: 70%;
  height: 70%;
  top: 15%;
  left: 15%;
  background: radial-gradient(
    circle at center,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 223, 186, 0.15) 20%,
    rgba(186, 255, 201, 0.1) 40%,
    rgba(186, 225, 255, 0.05) 60%,
    transparent 80%
  );
  border-radius: 50%;
  z-index: 1;
  animation: energyPulse 4s infinite alternate;
  filter: blur(calc(5px * var(--size-multiplier)));
  /* Replace mix-blend-mode with opacity for Safari compatibility */
  opacity: 0.7;
  /* Safari-specific fixes */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Safari fallback without mix-blend-mode */
@supports not (mix-blend-mode: screen) {
  .circulate .energy-glow {
    opacity: 0.5;
  }
}

/* Add a second energy glow for more dynamic effect with Safari compatibility */
.circulate .energy-glow::after {
  content: "";
  position: absolute;
  width: 80%;
  height: 80%;
  top: 10%;
  left: 10%;
  background: radial-gradient(
    circle at center,
    rgba(255, 186, 186, 0.15) 0%,
    rgba(255, 186, 255, 0.1) 30%,
    rgba(186, 186, 255, 0.05) 60%,
    transparent 80%
  );
  border-radius: 50%;
  animation: energyPulse 5s infinite alternate-reverse;
  filter: blur(calc(4px * var(--size-multiplier)));
  /* Replace mix-blend-mode with opacity for Safari compatibility */
  opacity: 0.6;
  /* Safari-specific fixes */
  -webkit-transform: translateZ(0);
  transform: translateZ(0);
}

/* Animation keyframes */
@keyframes float {
  0%,
  100% {
    transform: translateY(0) translateX(0);
  }
  25% {
    transform: translateY(calc(-5px * var(--size-multiplier))) translateX(calc(3px * var(--size-multiplier)));
  }
  50% {
    transform: translateY(0) translateX(calc(5px * var(--size-multiplier)));
  }
  75% {
    transform: translateY(calc(5px * var(--size-multiplier))) translateX(calc(2px * var(--size-multiplier)));
  }
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  from {
    opacity: 0.5;
    filter: blur(calc(0.5px * var(--size-multiplier)));
  }
  to {
    opacity: 0.8;
    filter: blur(calc(1px * var(--size-multiplier)));
  }
}

@keyframes particleFloat {
  0%,
  100% {
    transform: translateY(0) translateX(0);
  }
  50% {
    transform: translateY(calc(8px * var(--size-multiplier))) translateX(calc(5px * var(--size-multiplier)));
  }
}

@keyframes energyPulse {
  0% {
    opacity: 0.3;
    filter: blur(calc(4px * var(--size-multiplier)));
    transform: scale(0.9);
  }
  50% {
    opacity: 0.6;
    filter: blur(calc(5px * var(--size-multiplier)));
    transform: scale(1);
  }
  100% {
    opacity: 0.4;
    filter: blur(calc(6px * var(--size-multiplier)));
    transform: scale(0.95);
  }
}

@keyframes moveReflection {
  0%,
  100% {
    opacity: 0.7;
    transform: rotate(-40deg) translateY(0);
  }
  50% {
    opacity: 0.9;
    transform: rotate(-35deg) translateY(calc(5px * var(--size-multiplier)));
  }
}

@keyframes humming {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Animation for the gradient border rotation */
@keyframes gradient-angle {
  to {
    --gradient-angle: 1turn;
  }
}
</style>
