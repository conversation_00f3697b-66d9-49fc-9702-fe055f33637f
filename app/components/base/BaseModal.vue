<template>
  <UModal :title="props.title">
    <template #body>
      <p>{{ props.description }}</p>
    </template>
    <template #footer="{ close }">
      <div class="ml-auto flex gap-2">
        <UButton
          label="Ok"
          color="neutral"
          variant="outline"
          @click="close"
        />
        <UButton
          v-for="button in props.buttons"
          :key="button.label"
          :label="button.label"
          :color="button.color"
          :variant="button.variant"
          :trailing-icon="button.trailingIcon"
          @click="button.onClick"
        />
      </div>
    </template>
  </UModal>
</template>

<script setup lang="ts">
const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  buttons: {
    type: Array as () => any[],
    default: () => []
  }
})
</script>
