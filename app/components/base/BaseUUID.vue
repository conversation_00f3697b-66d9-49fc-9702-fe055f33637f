<template>
  <UTooltip :text="$t('ui.labels.clickToCopy')">
    <div
      class="cursor-pointer transition-colors hover:text-primary hover:underline font-mono text-xs break-all"
      @click="copyToClipboard"
    >
      {{ shortenText(value, 20) }}
    </div>
  </UTooltip>
</template>

<script setup lang="ts">
const { t } = useI18n()
const props = defineProps({
  value: {
    type: String,
    required: true
  }
})

const copyToClipboard = async () => {
  try {
    await navigator.clipboard.writeText(props.value)
    // Show success notification
    const toast = useToast()
    toast.add({
      title: t('ui.labels.copiedToClipboard'),
      description: t('UUID has been copied to clipboard'),
      color: 'success'
    })
  } catch (err) {
    console.error(t('ui.errors.failedToCopy'), err)
    // Fallback for older browsers
    const textArea = document.createElement('textarea')
    textArea.value = props.value
    document.body.appendChild(textArea)
    textArea.select()
    document.execCommand('copy')
    document.body.removeChild(textArea)

    const toast = useToast()
    toast.add({
      title: t('ui.labels.copiedToClipboard'),
      description: t('UUID has been copied to clipboard'),
      color: 'success'
    })
  }
}
</script>
