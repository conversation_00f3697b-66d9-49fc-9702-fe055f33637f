<script setup lang="ts">
const { videoDimensions, videoDimension } = useVideoDimensions()
const props = defineProps({
  options: {
    type: Array as () => string[],
    default: () => []
  }
})

const filteredVideoDimensions = computed(() => {
  return videoDimensions.filter((item: any) =>
    props.options.includes(item.value)
  )
})

watch(() => props.options, (newValue) => {
  if (!newValue.includes(videoDimension.value)) {
    videoDimension.value = newValue[0] as any
  }
})
</script>

<template>
  <URadioGroup
    v-model="videoDimension"
    indicator="hidden"
    variant="card"
    :items="filteredVideoDimensions"
    size="xs"
    :ui="{
      fieldset: 'flex flex-wrap gap-2 flex-row',
      item: 'flex py-0 !px-0 items-center justify-center rounded-lg border border-muted/50 hover:bg-muted/50'
    }"
  >
    <template #label="{ item }">
      <div class="flex flex-col items-center min-w-20 h-full">
        <div class="p-2">
          <div
            :class="item.classExample"
            class="flex items-center justify-center rounded-md border border-white/20 dark:bg-muted/50 bg-gray-200"
          >
            <UIcon
              name="ion:image"
              class="w-4 h-4 text-gray-500"
            />
          </div>
        </div>
        <div
          class="flex flex-col !py-1.5 dark:bg-transparent bg-white"
        >
          <span class="text-xs">{{ item.label }}</span>
        </div>
      </div>
    </template>
  </URadioGroup>
</template>
