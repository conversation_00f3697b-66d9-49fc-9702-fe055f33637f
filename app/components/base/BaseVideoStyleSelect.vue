<script setup lang="ts">
interface VideoStyle {
  id: string
  name: string
  description: string
  icon: string
}

interface VideoStyleSelectProps {
  modelValue?: string | null
}

const props = defineProps<VideoStyleSelectProps>()
const emit = defineEmits<{
  'update:modelValue': [value: string]
}>()

const { t } = useI18n()

// Video-specific styles
const videoStyles: VideoStyle[] = [
  {
    id: 'cinematic',
    name: 'Cinematic',
    description: 'Professional movie-like quality with dramatic lighting',
    icon: 'lucide:film'
  },
  {
    id: 'realistic',
    name: 'Realistic',
    description: 'Natural, lifelike video style',
    icon: 'lucide:eye'
  },
  {
    id: 'animated',
    name: 'Animated',
    description: 'Cartoon or animated style video',
    icon: 'lucide:play-circle'
  },
  {
    id: 'artistic',
    name: 'Artistic',
    description: 'Creative and stylized video effects',
    icon: 'lucide:palette'
  },
  {
    id: 'documentary',
    name: 'Documentary',
    description: 'Natural documentary-style footage',
    icon: 'lucide:camera'
  },
  {
    id: 'vintage',
    name: 'Vintage',
    description: 'Retro or vintage video aesthetic',
    icon: 'lucide:clock'
  }
]

const isModalOpen = ref(false)
const selectedStyle = ref<string | null>(props.modelValue || null)
const tempSelectedStyle = ref<string | null>(null)

const selectedStyleObject = computed(() => {
  return videoStyles.find(style => style.name === selectedStyle.value)
})

const openModal = () => {
  tempSelectedStyle.value = selectedStyle.value
  isModalOpen.value = true
}

const selectTempStyle = (styleName: string) => {
  tempSelectedStyle.value = styleName
}

const confirmSelection = () => {
  if (tempSelectedStyle.value) {
    selectedStyle.value = tempSelectedStyle.value
    emit('update:modelValue', tempSelectedStyle.value)
  }
  isModalOpen.value = false
}

const cancelSelection = () => {
  tempSelectedStyle.value = selectedStyle.value
  isModalOpen.value = false
}

watch(
  () => props.modelValue,
  (newValue) => {
    selectedStyle.value = newValue || null
  }
)
</script>

<template>
  <div>
    <!-- Button to open Modal -->
    <UButton
      :label="selectedStyle ? selectedStyle : t('videoStyles.selectVideoStyle')"
      :icon="selectedStyleObject ? selectedStyleObject.icon : 'lucide:film'"
      color="neutral"
      variant="outline"
      trailing-icon="lucide:chevron-down"
      v-bind="$attrs"
      :ui="{
        trailingIcon: 'ml-auto'
      }"
      @click="openModal"
    />

    <!-- Video Style Selection Modal -->
    <UModal
      v-model:open="isModalOpen"
      :title="t('videoStyles.selectVideoStyle')"
      :ui="{ footer: 'justify-end', content: 'max-w-4xl' }"
    >
      <template #body>
        <div class="space-y-3 max-h-96 overflow-y-auto">
          <UCard
            v-for="style in videoStyles"
            :key="style.id"
            class="cursor-pointer transition-all duration-200 hover:shadow-md"
            :class="{
              'ring-1 ring-primary-500 bg-primary-50 dark:bg-primary-950':
                tempSelectedStyle === style.name,
              'hover:bg-gray-50 dark:hover:bg-gray-800':
                tempSelectedStyle !== style.name
            }"
            @click="selectTempStyle(style.name)"
          >
            <div class="flex items-start gap-3">
              <UIcon
                :name="style.icon"
                class="w-5 h-5 text-primary-500 mt-0.5"
              />
              <div class="flex-1">
                <h3 class="font-medium text-sm">
                  {{ style.name }}
                </h3>
                <p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
                  {{ style.description }}
                </p>
              </div>
              <UIcon
                v-if="tempSelectedStyle === style.name"
                name="lucide:check"
                class="w-4 h-4 text-primary-500"
              />
            </div>
          </UCard>
        </div>
      </template>

      <template #footer>
        <div class="flex gap-2">
          <UButton
            color="neutral"
            variant="outline"
            :label="t('cancel')"
            @click="cancelSelection"
          />
          <UButton
            color="primary"
            :label="t('confirm')"
            :disabled="!tempSelectedStyle"
            @click="confirmSelection"
          />
        </div>
      </template>
    </UModal>
  </div>
</template>
