<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import WaveformPlayer from '../WaveformPlayer.vue'

const route = useRoute()
const router = useRouter()
const historyStore = useHistoryStore()
const { showDetailModal, historyDetail } = storeToRefs(historyStore)

interface Props {
  data?: any
  audioUrl?: string
  title?: string
  prompt?: string
  preset?: string
  voice?: string
  duration?: string
  orientation?: string
  loading?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  data: () => ({}),
  audioUrl: '',
  title: '',
  prompt: '',
  preset: '',
  voice: '',
  duration: '',
  orientation: 'vertical',
  loading: false
})

const lastAudio = computed(() => {
  return (
    props.data?.generated_audio?.[props.data?.generated_audio?.length - 1] || ''
  )
})

const audioUrl = computed(() => {
  // Use individual prop first, then fall back to data prop
  return props.audioUrl || lastAudio.value?.audio_url || ''
})

const title = computed(() => {
  // Use individual prop first, then fall back to data prop
  return (
    props.title
    || props.data?.input_text
    || props.data?.name
    || 'Generated Speech'
  )
})

const prompt = computed(() => {
  // Use individual prop first, then fall back to data prop
  return (
    props.prompt
    || props.data?.input_text
    || props.data?.custom_prompt
    || 'No prompt available'
  )
})

const model = computed(() => {
  // Use individual prop first, then fall back to data prop
  return (
    props.preset
    || props.data?.model_name
    || props.data?.model
    || 'Speech Model'
  )
})

const voice = computed(() => {
  // Use individual prop first, then fall back to data prop
  return props.voice || props.data?.voice || 'Default Voice'
})

const isFullScreenOpen = ref(false)
const showResult = ref(false)
const isHovered = ref(false)
const isTouchDevice = ref(false)

// Check if it's a touch device on component mount
onMounted(() => {
  isTouchDevice.value
    = 'ontouchstart' in window || navigator.maxTouchPoints > 0
})

const menuClass = computed(() => {
  return 'opacity-0 transition-opacity duration-200 -z-10'
})

const openFullScreen = () => {
  // On touch devices, first tap shows overlay, second tap opens fullscreen
  if (isTouchDevice.value && !isHovered.value) {
    isHovered.value = true
    return
  }

  historyDetail.value = props.data as any
  showDetailModal.value = true
  // Update the URL to include the ID for navigation
  if (props.data.uuid) {
    router.push({ query: { ...route.query, uuid: props.data.uuid } })
  }
  isHovered.value = false
}

const closeFullScreen = () => {
  isFullScreenOpen.value = false
}

const showAudioResult = () => {
  showResult.value = true
}

const hideAudioResult = () => {
  showResult.value = false
}

const showMenu = ref(false)
</script>

<template>
  <!-- Card View -->
  <HistoryWrapper
    :id="data.id"
    :type="data.type"
    :status="data.status"
    :hard-show-menu="!showMenu"
    :is-opening-menu="showMenu"
    @menu="showMenu = true"
  >
    <UPageCard
      :ui="{
        container: 'lg:items-start p-0 sm:p-0',
        root: 'overflow-hidden relative group'
      }"
      class="speech-card cursor-pointer h-full"
    >
      <div class="relative w-full h-full">
        <div class="p-4">
          <div
            class="mb-4 cursor-pointer mt-2"
            @click="openFullScreen"
          >
            <p
              class="text-xs text-gray-600 dark:text-gray-300 line-clamp-1 hover:text-primary-500 break-all"
            >
              {{ prompt }}
            </p>
          </div>

          <!-- Loading State -->
          <div
            v-if="loading || data.status === 1"
            class="w-full h-32 bg-gray-200 dark:bg-neutral-800 rounded-lg flex flex-col items-center justify-center mb-4"
          >
            <UIcon
              name="svg-spinners:blocks-wave"
              class="w-12 h-12 text-neutral-400/50 dark:text-neutral-700 mb-2"
            />
            <span class="text-xs text-gray-500 dark:text-gray-400">
              {{ $t('ui.messages.speechGenerating') }}
            </span>
          </div>

          <!-- Error State -->
          <div
            v-else-if="data?.status === 3"
            class="w-full h-32 text-error gap-2 bg-gray-200 dark:bg-neutral-800 rounded-lg flex flex-col items-center justify-center mb-4"
          >
            <UIcon
              name="material-symbols:error"
              class="w-8 h-8"
            />
            <div class="flex flex-col gap-1 items-center justify-center">
              <div class="text-xs">
                {{ $t("ui.errors.generationFailed") }}
              </div>
              <div class="text-xs px-4 text-error/70 text-center">
                {{ data?.error_message }}
              </div>
            </div>
          </div>

          <!-- Audio Result -->
          <div
            v-else-if="showResult && audioUrl"
            class="mb-4"
          >
            <div class="flex items-center justify-between mb-2">
              <span class="text-xs font-medium text-gray-700 dark:text-gray-300">
                {{ $t('ui.labels.generatedAudio') }}
              </span>
              <UButton
                icon="i-lucide-x"
                size="xs"
                color="neutral"
                variant="ghost"
                @click="hideAudioResult"
              />
            </div>
            <WaveformPlayer
              :audio-url="audioUrl"
              :fullscreen="false"
            />
          </div>

          <!-- Default Audio Player -->
          <div
            v-else
            class="mb-4"
          >
            <WaveformPlayer
              :audio-url="audioUrl"
              :fullscreen="false"
            />
          </div>
        </div>

        <!-- Hover Overlay -->
        <div
          class="absolute inset-0 bg-gradient-to-b from-black/70 via-black/60 to-black/80 backdrop-blur-sm flex flex-col justify-between p-4"
          :class="{ 'opacity-100 z-10': showMenu, [menuClass]: true }"
        >
          <div>
            <div class="flex justify-between items-start gap-2">
              <div
                class="dark:text-white text-gray-100 font-medium text-xs line-clamp-3 cursor-pointer hover:underline hover:text-primary group-hover:translate-y-0"
                :class="{ 'translate-y-0 opacity-100': showMenu, [menuClass]: true }"
                @click.stop="openFullScreen"
              >
                {{ prompt }}
              </div>
            </div>
            <div>
              <HistoryMenu
                :data="data"
                show-close-button
                @close="showMenu = false"
              />
            </div>
          </div>
        </div>
      </div>
    </UPageCard>
  </HistoryWrapper>
</template>

<style scoped>
.line-clamp-2 {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

/* Add line-clamp utility if not available in your Tailwind config */
.line-clamp-3 {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}
</style>
