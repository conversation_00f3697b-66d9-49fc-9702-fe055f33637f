<template>
  <UCard
    :ui="{
      body: '!p-0'
    }"
    v-bind="$attrs"
    class="group transition-all duration-300"
  >
    <div
      class="flex flex-row items-center"
      :class="{ 'p-3': true }"
    >
      <div>
        <USkeleton class="h-10 w-10 rounded-full" />
      </div>
      <div class="flex-1 ml-2 flex flex-col gap-1">
        <div class="flex flex-row items-center gap-2">
          <USkeleton class="h-4 w-[100px]" />
          <div class="flex flex-row items-center gap-1">
            <USkeleton class="h-3 w-[30px]" />
            <USkeleton class="h-3 w-[30px]" />
          </div>
        </div>
        <div
          class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 group-hover:line-clamp-none pr-6"
        >
          <USkeleton class="h-7 w-full" />
        </div>
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts"></script>
