import BaseModal from '~/components/base/BaseModal.vue'

export function useAuthorize() {
  const authStore = useAuthStore()
  const router = useRouter()
  const overlay = useOverlay()
  const modal = overlay.create(BaseModal)
  const { t } = useI18n()
  const { isAuthenticated, isNotVerifyAccount, user_credit }
    = storeToRefs(authStore)
  const authorize = (callback: () => void) => {
    if (!isAuthenticated.value) {
      router.push('/auth/login')
      return
    }
    // check if user inactive
    if (isNotVerifyAccount.value) {
      modal.open({
        title: t('Your account is not verified'),
        description: t(
          'Your account is not verified. Please verify your account to continue'
        )
      })
      return
    }
    // check if user has enough credit
    if (user_credit.value?.available_credit < 1) {
      modal.open({
        title: t('Not enough credit'),
        description: t('Your account does not have enough credit. Please top up your account to continue.'),
        buttons: [
          {
            label: t('Top up now'),
            color: 'primary',
            trailingIcon: 'ep:right',
            onClick: () => {
              router.push('/profile/credits')
              modal.close()
            }
          }
        ]
      })
      return
    }
    callback()
  }

  return {
    authorize
  }
}
