import { createSharedComposable } from '@vueuse/core'

export interface ConfirmOptions {
  title: string
  description?: string
  icon?: string
  confirmText?: string
  cancelText?: string
  onConfirm: () => void | Promise<void>
}

const _useConfirm = () => {
  const isOpen = ref(false)
  const isLoading = ref(false)
  const options = ref<ConfirmOptions | null>(null)

  const openConfirm = (confirmOptions: ConfirmOptions) => {
    options.value = confirmOptions
    isOpen.value = true
    isLoading.value = false
  }

  const closeConfirm = () => {
    isOpen.value = false
    isLoading.value = false
    options.value = null
  }

  const handleConfirm = async () => {
    if (options.value?.onConfirm) {
      try {
        isLoading.value = true
        await options.value.onConfirm()
        closeConfirm()
      } catch (error) {
        isLoading.value = false
        // Don't close the modal if there's an error
        // Let the onConfirm handler deal with error handling
        throw error
      }
    } else {
      closeConfirm()
    }
  }

  const handleCancel = () => {
    if (!isLoading.value) {
      closeConfirm()
    }
  }

  return {
    isOpen,
    isLoading,
    options,
    openConfirm,
    closeConfirm,
    handleConfirm,
    handleCancel
  }
}

export const useConfirm = createSharedComposable(_useConfirm)
