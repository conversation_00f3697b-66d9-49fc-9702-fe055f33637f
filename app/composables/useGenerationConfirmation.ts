import { createSharedComposable } from '@vueuse/core'

export interface GenerationConfirmationOptions {
  generationType: 'image' | 'video' | 'speech' | 'dialog'
  hasChanges: ComputedRef<boolean>
  hasResult: ComputedRef<boolean>
  onGenerate: () => Promise<void>
  validationRules?: (() => { isValid: boolean, message?: string })[]
}

const _useGenerationConfirmation = () => {
  const { openConfirm } = useConfirm()
  // const { t } = useI18n()
  const $nuxtApp = useNuxtApp()
  const t = $nuxtApp.$i18n.t
  const toast = useToast()
  const { handleGenerationError } = useErrorHandler()

  const getGenerationTitle = (type: string) => {
    const titles = {
      image: t('Regenerate Image'),
      video: t('Regenerate Video'),
      speech: t('Regenerate Speech'),
      dialog: t('Regenerate Dialog')
    }
    return titles[type as keyof typeof titles] || t('Regenerate')
  }

  const getGenerationDescription = (type: string) => {
    const descriptions = {
      image: t('You haven\'t made any changes to the settings. Are you sure you want to regenerate the same image?'),
      video: t('You haven\'t made any changes to the settings. Are you sure you want to regenerate the same video?'),
      speech: t('You haven\'t made any changes to the settings. Are you sure you want to regenerate the same speech?'),
      dialog: t('You haven\'t made any changes to the settings. Are you sure you want to regenerate the same dialog?')
    }
    return descriptions[type as keyof typeof descriptions] || t('You haven\'t made any changes to the settings. Are you sure you want to regenerate?')
  }

  const validateGeneration = (validationRules?: (() => { isValid: boolean, message?: string })[]) => {
    if (!validationRules) return { isValid: true }

    for (const rule of validationRules) {
      const result = rule()
      if (!result.isValid) {
        if (result.message) {
          toast.add({
            id: 'validation-error',
            title: t('Validation Error'),
            description: result.message,
            color: 'error'
          })
        }
        return { isValid: false, message: result.message }
      }
    }

    return { isValid: true }
  }

  const handleGeneration = async (options: GenerationConfirmationOptions) => {
    const { generationType, hasChanges, hasResult, onGenerate, validationRules } = options

    try {
      // Run validation first
      const validation = validateGeneration(validationRules)
      if (!validation.isValid) {
        return false
      }

      // Check if user has made any changes and there's already a result
      if (!hasChanges.value && hasResult.value) {
        // Show confirmation dialog
        openConfirm({
          title: getGenerationTitle(generationType),
          description: getGenerationDescription(generationType),
          icon: 'lucide:refresh-cw',
          confirmText: t('Yes, Regenerate'),
          cancelText: t('Cancel'),
          onConfirm: async () => {
            try {
              await onGenerate()
            } catch (error) {
              handleGenerationError(error, generationType)
              throw error // Re-throw to let the confirm modal handle it
            }
          }
        })
      } else {
        // Proceed with generation if changes were made or no previous result
        await onGenerate()
      }

      return true
    } catch (error) {
      handleGenerationError(error, generationType)
      return false
    }
  }

  return {
    handleGeneration,
    validateGeneration,
    getGenerationTitle,
    getGenerationDescription
  }
}

export const useGenerationConfirmation = createSharedComposable(_useGenerationConfirmation)
