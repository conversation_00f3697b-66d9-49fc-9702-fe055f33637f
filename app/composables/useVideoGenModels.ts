export function useVideoGenModels() {
  const models = [
    {
      label: 'Veo 2',
      value: 'veo-2',
      icon: 'hugeicons:ai-chip',
      options: ['style', 'videoDimensions', 'yourImage'],
      ratios: ['16:9', '9:16']
    },
    {
      label: 'Veo 3',
      value: 'veo-3',
      icon: 'hugeicons:ai-chip',
      options: ['style', 'videoDimensions'],
      ratios: ['16:9']
    }
  ]
  const model = useCookie<any>('video-gen-model', { default: () => models[0] })

  return {
    models,
    model
  }
}
