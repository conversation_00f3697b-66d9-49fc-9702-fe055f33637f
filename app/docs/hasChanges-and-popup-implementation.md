# HasChanges và Popup Implementation Guide

## Tổng quan

Tài liệu này mô tả cách implementation `hasChanges` và popup confirmation được thực hiện trong các trang generation (Image, Video, Speech, Dialogue).

## Kiến trúc

### 1. Core Composables

#### `useGenerationConfirmation`
- Xử lý logic confirmation popup thống nhất
- Tích hợp validation rules
- Error handling thống nhất

#### `useErrorHandler`
- Xử lý lỗi thống nhất across toàn bộ app
- Logging và reporting
- Toast notifications

### 2. Utility Functions

#### `generationValidation.ts`
- `compareImageArrays()`: So sánh mảng images hiệu quả
- `compareFileArrays()`: So sánh mảng files hiệu quả  
- `compareDialogArrays()`: So sánh mảng dialogs hiệu quả
- `commonValidationRules`: Validation rules tái sử dụng
- `runValidations()`: Chạy batch validations

## Implementation Pattern

### 1. Setup trong mỗi trang

```typescript
// Import utilities
import { compareImageArrays, commonValidationRules } from '~/utils/generationValidation'

// Setup composables
const { handleGeneration } = useGenerationConfirmation()
const { handleGenerationError } = useErrorHandler()

// Store initial values
const initialValues = ref({
  prompt: '',
  model: models[0],
  // ... other fields
})

// Initialize on mount
onMounted(() => {
  initialValues.value = {
    prompt: prompt.value,
    model: model.value,
    // ... copy current values
  }
})
```

### 2. HasChanges Logic

```typescript
const hasChanges = computed(() => {
  // Basic field comparisons
  const basicFieldsChanged = (
    prompt.value !== initialValues.value.prompt
    || model.value?.value !== initialValues.value.model?.value
    // ... other basic fields
  )

  // Complex comparisons using utilities
  const complexFieldsChanged = compareImageArrays(
    selectedImages.value, 
    initialValues.value.selectedImages
  )

  return basicFieldsChanged || complexFieldsChanged
})
```

### 3. Generation Handler

```typescript
const onGenerate = async () => {
  if (!isAuthenticated.value) {
    router.push('/auth/login')
    return
  }

  // Define validation rules
  const validationRules = [
    commonValidationRules.requiredText(
      prompt.value, 
      t('Please enter a prompt')
    ),
    commonValidationRules.requiredValue(
      selectedVoice.value,
      t('Please select a voice')
    )
  ]

  // Use unified generation confirmation logic
  await handleGeneration({
    generationType: 'image', // 'video', 'speech', 'dialog'
    hasChanges,
    hasResult: computed(() => !!result.value),
    onGenerate: performGeneration,
    validationRules
  })
}
```

### 4. Update Initial Values After Success

```typescript
const performGeneration = async () => {
  const result = await generateContent()
  
  if (result) {
    // Show success toast
    toast.add({
      id: 'success',
      title: 'Generation Complete',
      description: 'Your content is ready!',
      color: 'success'
    })

    // Update initial values to reset hasChanges
    initialValues.value = {
      prompt: prompt.value,
      model: model.value,
      // ... copy current values
    }
  }
}
```

## Performance Optimizations

### 1. Efficient Array Comparisons

Thay vì sử dụng `JSON.stringify()` hoặc deep comparison cho mọi thứ:

```typescript
// ❌ Không hiệu quả
const dialogsChanged = JSON.stringify(dialogs.value) !== JSON.stringify(initialValues.value.dialogs)

// ✅ Hiệu quả
const dialogsChanged = compareDialogArrays(dialogs.value, initialValues.value.dialogs)
```

### 2. Debounced HasChanges (Optional)

Cho các form phức tạp:

```typescript
import { createDebouncedHasChanges } from '~/utils/generationValidation'

const hasChanges = createDebouncedHasChanges(() => {
  // comparison logic
}, 100) // 100ms debounce
```

## Error Handling

### 1. Validation Errors

```typescript
const validationRules = [
  commonValidationRules.requiredText(prompt.value, 'Prompt is required'),
  commonValidationRules.requiredArray(dialogs.value, 'At least one dialog required'),
  commonValidationRules.requiredMultiple([voice1.value, voice2.value], 'Both voices required')
]
```

### 2. Generation Errors

Errors được handle tự động bởi `useGenerationConfirmation` và `useErrorHandler`:

- Console logging với context
- Toast notifications
- Error reporting (có thể extend)

## Testing

### 1. Unit Tests

```typescript
// tests/generationValidation.test.ts
import { compareImageArrays, commonValidationRules } from '~/utils/generationValidation'

describe('compareImageArrays', () => {
  it('should detect changes correctly', () => {
    const images1 = [{ src: 'img1.jpg', alt: 'Image 1' }]
    const images2 = [{ src: 'img2.jpg', alt: 'Image 1' }]
    expect(compareImageArrays(images1, images2)).toBe(true)
  })
})
```

### 2. Integration Tests

Test các trang generation để đảm bảo:
- HasChanges hoạt động chính xác
- Popup hiện đúng lúc
- Validation rules hoạt động
- Error handling đúng

## Best Practices

### 1. Consistency
- Sử dụng cùng pattern cho tất cả generation pages
- Sử dụng shared utilities và composables
- Consistent error messages và validation

### 2. Performance
- Sử dụng efficient comparison functions
- Avoid unnecessary deep comparisons
- Consider debouncing cho complex forms

### 3. User Experience
- Clear validation messages
- Consistent confirmation dialogs
- Proper loading states
- Error recovery options

### 4. Maintainability
- Centralized logic trong composables
- Reusable validation rules
- Comprehensive testing
- Clear documentation

## Troubleshooting

### Common Issues

1. **HasChanges không update**: Kiểm tra reactive references và computed dependencies
2. **Popup không hiện**: Kiểm tra hasResult computed và hasChanges logic
3. **Validation không hoạt động**: Kiểm tra validation rules và error handling
4. **Performance issues**: Sử dụng efficient comparison functions

### Debug Tips

```typescript
// Debug hasChanges
watchEffect(() => {
  console.log('HasChanges:', hasChanges.value)
  console.log('Current values:', { prompt: prompt.value, model: model.value })
  console.log('Initial values:', initialValues.value)
})
```
