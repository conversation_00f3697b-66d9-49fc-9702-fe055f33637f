<script setup lang="ts">
const { t } = useI18n()
</script>

<template>
  <div class="relative flex-col">
    <div class="flex justify-center px-4 pt-10">
      <UButton
        icon="i-lucide-chevron-left"
        to="/"
        size="xl"
        color="neutral"
        variant="subtle"
        class="absolute left-8 top-8 rounded-full z-10"
        :aria-label="t('auth.backToHome')"
        :title="t('auth.backToHome')"
      />

      <UPageCard
        variant="ghost"
        class="max-w-sm w-full"
      >
        <div class="text-center mb-4">
          <BaseAppTitle
            class="justify-center text-center flex mx-auto !text-5xl"
          />
        </div>
        <slot />
      </UPageCard>

      <LazyLayoutStarBg />
      <BaseLoadingOverlay />
      <BaseConfirmModal />
    </div>
    <AppFooter />
  </div>
</template>
