<script setup lang="ts">
const { t } = useI18n()
const productStore = useProductStore()
const { getServicePriceByModelName } = storeToRefs(productStore)

const tiers = computed(() => {
  return [
    {
      id: 'free',
      title: t('Free'),
      price: '$0',
      description: t('Forever'),
      button: {
        label: 'Get started',
        variant: 'soft' as const,
        color: 'neutral',
        onclick: () => navigateTo('/auth/signup')
      }
    },
    {
      id: 'premium',
      title: t('Premium'),
      description: t('Auto upgrade after buy credits'),
      price: t('Pay as you go'),
      button: {
        label: 'Buy credits',
        variant: 'soft' as const,
        color: 'primary'
      },
      highlight: true
    },
    {
      id: 'enterprise',
      title: t('Enterprise'),
      price: t('Contact us'),
      description: t('For large organizations.'),
      button: {
        label: t('Contact sales'),
        variant: 'soft' as const,
        color: 'neutral'
      }
    }
  ]
})

const sections = computed(() => {
  return [
    {
      id: 'imagen',
      title: t('Imagen'),
      features: [
        {
          id: 'imagen-flash',
          title: t('Gemini 2.0 Flash'),
          tiers: {
            free: false,
            premium: {
              priceUnit: 0.039, // $0.039/image
              discount: 0.5, // 50% discount
              unit: 'image',
              credits: getServicePriceByModelName.value('imagen-flash')?.effective_price
            },
            enterprise: t('Custom')
          }
        },
        {
          id: 'imagen-4-fast',
          title: t('Imagen 4 Fast'),
          tiers: {
            free: false,
            premium: '$0,02/image',
            enterprise: t('Custom')
          }
        },
        {
          id: 'imagen-4',
          title: t('Imagen 4  '),
          tiers: {
            free: false,
            premium: '$0,04/image',
            enterprise: t('Custom')
          }
        },
        {
          id: 'imagen-4-ultra',
          title: t('Imagen 4 Ultra'),
          tiers: {
            free: false,
            premium: '$0,06/image',
            enterprise: t('Custom')
          }
        },
        {
          id: 'image-style',
          title: t('Image Style'),
          tiers: {
            free: false,
            premium: t('15+ Styles'),
            enterprise: t('15+ Styles')
          }
        },
        // Aspect Ratio
        {
          id: 'image-dimensions',
          title: t('Image Aspect Ratio'),
          tiers: {
            free: false,
            premium: t('Support multiple aspect ratio'),
            enterprise: t('Support multiple aspect ratio')
          }
        },
        {
          id: 'image-reference',
          title: t('Image Reference'),
          tiers: {
            free: false,
            premium: t('Up to 10MB'),
            enterprise: t('Up to 10MB')
          }
        }
      ]
    },
    {
      id: 'video-gen',
      title: t('Video Gen'),
      features: [
        {
          id: 'veo-2',
          title: t('Veo 2'),
          tiers: {
            free: false,
            premium: '$0,5/video',
            enterprise: t('Custom')
          }
        },
        {
          id: 'veo-3-fast',
          title: t('Veo 3 Fast'),
          tiers: {
            free: false,
            premium: '$0,5/video',
            enterprise: t('Custom')
          }
        },
        {
          id: 'veo-3',
          title: t('Veo 3'),
          tiers: {
            free: false,
            premium: '$0,75/video',
            enterprise: t('Custom')
          }
        }
      ]
    },
    {
      id: 'speech-gen',
      title: t('Speech Gen'),
      features: [
        {
          id: 'gemini-2.5-pro',
          title: t('Gemini 2.5 Pro'),
          tiers: {
            free: false,
            premium: '$20/1M characters',
            enterprise: t('Custom')
          }
        },
        {
          id: 'gemini-2.5-flash',
          title: t('Gemini 2.5 Flash'),
          tiers: {
            free: false,
            premium: '$40/1M characters',
            enterprise: t('Custom')
          }
        }
      ]
    }
  ]
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('pricing.title')"
        :description="$t('Simple and flexible. Only pay for what you use.')"
      />
    </UContainer>
    <UPageSection
      :ui="{
        container: '!pt-10'
      }"
    >
      <UPricingTable
        :tiers="tiers"
        :sections="sections"
      >
        <!-- Customize specific tier title -->
        <template #premium-title="{ tier }">
          <div class="flex items-center gap-2">
            <UIcon
              name="i-lucide-crown"
              class="size-4 text-amber-500"
            />
            {{ tier.title }}
          </div>
        </template>

        <!-- Customize specific section title -->
        <template #section-security-title="{ section }">
          <div class="flex items-center gap-2">
            <UIcon
              name="i-lucide-shield-check"
              class="size-4 text-green-500"
            />
            <span class="font-semibold text-green-700">{{
              section.title
            }}</span>
          </div>
        </template>

        <!-- Customize specific feature value -->
        <template #feature-value="{ feature, tier }">
          <template v-if="feature.tiers?.[tier.id]?.priceUnit">
            <div>
              {{ feature.tiers[tier.id].priceUnit * }}
            </div>
          </template>
          <template v-else-if="feature.tiers?.[tier.id]">
            {{ feature.tiers[tier.id] }}
          </template>
          <UIcon
            v-else
            name="i-lucide-x"
            class="size-5 text-muted"
          />
        </template>
      </UPricingTable>
    </UPageSection>
  </UPage>
</template>
