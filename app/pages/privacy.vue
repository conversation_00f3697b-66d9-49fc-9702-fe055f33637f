<script setup lang="ts">
const { t } = useI18n()

useSeoMeta({
  title: `${t('privacy.title')} - Imagen`,
  description: t('privacy.description'),
  ogTitle: `${t('privacy.title')} - Imagen`,
  ogDescription: t('privacy.description')
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('privacy.title')"
        :description="$t('privacy.description')"
      />
    </UContainer>
    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <h2>{{ $t("privacy.informationWeCollect") }}</h2>
        <p>
          {{ $t("privacy.informationWeCollectDescription") }}
        </p>

        <h2>{{ $t("privacy.howWeUseInformation") }}</h2>
        <p>
          {{ $t("privacy.howWeUseInformationDescription") }}
        </p>

        <h2>{{ $t("privacy.informationSharing") }}</h2>
        <p>
          {{ $t("privacy.informationSharingDescription") }}
        </p>

        <h2>{{ $t("privacy.dataSecurity") }}</h2>
        <p>
          {{ $t("privacy.dataSecurityDescription") }}
        </p>

        <h2>{{ $t("privacy.contactUs") }}</h2>
        <p>
          {{ $t("privacy.contactUsDescription") }}
        </p>
      </div>
    </UContainer>
  </UPage>
</template>
