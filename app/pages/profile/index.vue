<script setup lang="ts">
import * as z from 'zod'
import type { FormSubmitEvent } from '@nuxt/ui'

const authStore = useAuthStore()
const { user, user_credit, loading, user_plan } = storeToRefs(authStore)
const fileRef = ref<HTMLInputElement>()
const { t } = useI18n()

const profileSchema = z.object({
  full_name: z
    .string({
      required_error: t('This field is required.')
    })
    .min(1, t('This field is required.'))
})

const profile = ref({
  full_name: user.value?.full_name || ''
})

watch(user, (newUser) => {
  profile.value.full_name = newUser?.full_name || ''
})

type ProfileSchema = z.output<typeof profileSchema>

const toast = useToast()
async function onSubmit(event: FormSubmitEvent<ProfileSchema>) {
  const result = await authStore.updateProfile({
    full_name: event.data.full_name,
    referral_link: 'fake'
  })

  if (result) {
    toast.add({
      title: t('Success'),
      description: t('Your account has been updated.'),
      icon: 'i-lucide-check',
      color: 'success'
    })
  }
}
</script>

<template>
  <UForm
    id="settings"
    :schema="profileSchema"
    :state="profile"
    @submit="onSubmit"
  >
    <UPageCard
      :title="$t('User Information')"
      variant="naked"
      orientation="horizontal"
      class="mb-4"
    >
      <UButton
        form="settings"
        :label="$t('Save changes')"
        color="neutral"
        type="submit"
        class="w-fit lg:ms-auto"
        :loading="loading"
      />
    </UPageCard>
    <UPageCard variant="subtle">
      <UFormField
        name="full_name"
        :label="$t('Full Name')"
        :description="
          $t('Will appear on receipts, invoices, and other communication.')
        "
        required
        class="flex max-sm:flex-col justify-between items-start gap-4"
        :ui="{ container: 'sm:w-1/3 w-full text-right' }"
      >
        <UInput
          v-model="profile.full_name"
          autocomplete="off"
          class="w-full"
        />
      </UFormField>
      <USeparator />
      <UFormField
        name="email"
        :label="$t('Email')"
        :description="
          $t('Used to sign in, for email receipts and product updates.')
        "
        class="flex max-sm:flex-col justify-between items-start gap-4"
        :ui="{ container: 'sm:w-1/3 w-full text-right' }"
      >
        <UInput
          v-model="user.email"
          type="email"
          autocomplete="off"
          class="w-full"
          disabled
        />
      </UFormField>
      <USeparator />
      <UFormField
        :label="$t('Current Plan')"
        :description="
          $t('When you buy credits, you will be upgraded to Premium Plan.')
        "
        class="flex max-sm:flex-col justify-between items-start gap-4"
      >
        <div>
          <span class="text-lg text-primary font-semibold">{{
            $t(user_plan?.product?.name || '')
          }}</span>
        </div>
      </UFormField>
      <UFormField
        :label="$t('Total Available Credits')"
        :description="$t('Active and valid credits only')"
        class="flex max-sm:flex-col justify-between items-start gap-4"
      >
        <div>
          <span class="text-lg text-primary font-semibold">{{
            formatNumber(user_credit?.available_credit || 0)
          }}</span>
        </div>
      </UFormField>
      <UFormField
        :label="$t('Locked Credits')"
        :description="$t('We lock your credits to perform transactions.')"
        class="flex max-sm:flex-col justify-between items-start gap-4"
      >
        <div>
          <span class="text-lg text-gray font-semibold">{{
            formatNumber(user_credit?.locked_credit || 0)
          }}</span>
        </div>
      </UFormField>
    </UPageCard>
  </UForm>
</template>
