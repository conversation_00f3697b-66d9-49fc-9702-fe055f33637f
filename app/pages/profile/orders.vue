<script setup lang="ts">
import type { TableColumn } from '@nuxt/ui'
import { getPaginationRowModel, type Row } from '@tanstack/table-core'
import type { User } from '~/types'
import { PaymentStatus } from '~~/types/index.d'

const { t } = useI18n()
const ordersStore = useOrdersStore()
const { loadings, orders, ordersTotal } = storeToRefs(ordersStore)
const UAvatar = resolveComponent('UAvatar')
const UBadge = resolveComponent('UBadge')
const BaseUUID = resolveComponent('BaseUUID')

const paymentStatus = (
  status: PaymentStatus | string
):
  | {
    label: string
    color: string
    class: string
    icon: string
  }
  | undefined => {
  switch (Number(status)) {
    case PaymentStatus.UNAVAILABLE:
      return {
        label: t('payment.status.unavailable'),
        color: 'text-gray-500 dark:text-gray-200',
        class:
          'bg-gray-100 text-gray-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-gray-900 dark:text-gray-300',
        icon: 'iconamoon:unavailable-thin'
      }
    case PaymentStatus.CREATED:
      return {
        label: t('payment.status.created'),
        color: 'text-blue-500',
        class:
          'bg-blue-100 text-blue-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-blue-900 dark:text-blue-300',
        icon: 'eos-icons:subscriptions-created-outlined'
      }
    case PaymentStatus.COMPLETED:
      return {
        label: t('payment.status.completed'),
        color: 'text-green-500',
        class:
          'bg-green-100 text-green-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300',
        icon: 'ix:success-filled'
      }
    case PaymentStatus.FAILED:
      return {
        label: t('payment.status.failed'),
        color: 'text-red-500',
        class:
          'bg-red-100 text-red-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-red-900 dark:text-red-300',
        icon: 'bx:error'
      }
    case PaymentStatus.CANCELED:
      return {
        label: t('payment.status.canceled'),
        color: 'text-gray-500 dark:text-gray-300',
        class:
          'bg-gray-100 text-gray-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-gray-900 dark:text-gray-300',
        icon: 'material-symbols:cancel-outline'
      }
    case PaymentStatus.PROCESSING:
      return {
        label: t('payment.status.processing'),
        color: 'text-yellow-500 dark:text-yellow-300',
        class:
          'bg-yellow-100 text-yellow-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-yellow-900 dark:text-yellow-300',
        icon: 'arcticons:goodtime'
      }
    case PaymentStatus.REFUND:
      return {
        label: t('payment.status.refund'),
        color: 'text-gray-900 dark:text-gray-300',
        class:
          'bg-gray-100 text-gray-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-gray-900 dark:text-gray-300',
        icon: 'gridicons:refund'
      }
    case PaymentStatus.PARTIAL_PAID:
      return {
        label: t('payment.status.partial_paid'),
        color: 'text-green-500',
        class:
          'bg-green-100 text-green-800 text-sm font-medium mr-2 px-2.5 py-0.5 rounded dark:bg-green-900 dark:text-green-300',
        icon: 'subway:part-of-circle-5'
      }
    default:
      return undefined
  }
}
const table = useTemplateRef('table')
const columns = computed(() => {
  return [
    {
      accessorKey: 'uuid',
      header: 'UUID',
      cell: ({ row }) => {
        return h(BaseUUID, { value: row.original.uuid })
      }
    },
    {
      accessorKey: 'platform',
      header: t('Payment method'),
      cell: ({ row }) => {
        return h('div', { class: 'flex items-center gap-3' }, [
          h(UAvatar, {
            size: 'lg',
            icon:
              row.original.platform === 'Stripe'
                ? 'i-simple-icons-stripe'
                : 'i-cryptocurrency-color-usdt'
          }),
          h('div', undefined, [
            h(
              'p',
              { class: 'font-medium text-highlighted' },
              row.original.platform
            ),
            h(BaseUUID, { value: row.original.external_order_id })
          ])
        ])
      }
    },
    {
      accessorKey: 'date',
      header: t('Purchase Date'),
      cell: ({ row }) => formatDateToLocale(row.original.created_at)
    },
    {
      accessorKey: 'quantity',
      header: t('Credits'),
      cell: ({ row }) => abbreviatedUnit(row.original.paid_credit)
    },
    {
      accessorKey: 'amount_divide_100',
      header: t('Payment amount'),
      cell: ({ row }) => formatCurrency(row.original.amount_divide_100)
    },
    {
      accessorKey: 'status',
      header: t('Status'),
      cell: ({ row }) => {
        const status = paymentStatus(row.original.status)
        if (!status) return h('span', undefined, row.original.status)
        return h(
          UBadge,
          { class: 'capitalize', variant: 'subtle', ...status },
          () => [h('span', undefined, status.label)]
        )
      }
    }
  ]
})

const pagination = ref({
  pageIndex: 0,
  pageSize: 10
})

watch(() => pagination.value.pageIndex, async (newIndex) => {
  await ordersStore.fetchOrders({
    items_per_page: pagination.value.pageSize,
    page: newIndex + 1
  })
})

onMounted(async () => {
  await ordersStore.fetchOrders({
    items_per_page: pagination.value.pageSize,
    page: pagination.value.pageIndex + 1
  })
})
</script>

<template>
  <div id="customers">
    <div class="mb-4">
      <h2 class="text-xl font-bold">
        {{ $t("Payment History") }}
      </h2>
      <p class="text-sm text-muted">
        {{
          $t(
            "Your payment history will appear here once you have made a purchase."
          )
        }}
      </p>
    </div>
    <UTable
      ref="table"
      v-model:pagination="pagination"
      class="shrink-0"
      :data="orders"
      :columns="columns"
      :loading="loadings.fetchOrders"
      :ui="{
        base: 'table-fixed border-separate border-spacing-0',
        thead: '[&>tr]:bg-elevated/50 [&>tr]:after:content-none',
        tbody: '[&>tr]:last:[&>td]:border-b-0',
        th: 'py-2 first:rounded-l-lg last:rounded-r-lg border-y border-default first:border-l last:border-r',
        td: 'border-b border-default'
      }"
    />

    <div
      class="flex items-center justify-between gap-3 border-t border-default pt-4 mt-auto"
    >
      <div />
      <div class="flex items-center gap-1.5">
        <UPagination
          :default-page="
            (table?.tableApi?.getState().pagination.pageIndex || 0) + 1
          "
          :items-per-page="table?.tableApi?.getState().pagination.pageSize"
          :total="ordersTotal"
          @update:page="(p: number) => table?.tableApi?.setPageIndex(p - 1)"
        />
      </div>
    </div>
  </div>
</template>
