<script setup lang="ts">
import * as z from 'zod'
import type { FormError, FormSubmitEvent } from '@nuxt/ui'

const { t } = useI18n()
const authStore = useAuthStore()
const { loading, error } = storeToRefs(authStore)
const toast = useToast()
const confirm = useConfirm()

const passwordSchema = z.object({
  current_password: z
    .string({
      required_error: t('This field is required.')
    })
    .min(8, t('Must be at least 8 characters')),
  new_password: z
    .string({
      required_error: t('This field is required.')
    })
    .min(8, t('Must be at least 8 characters')),
  confirm_password: z
    .string({
      required_error: t('This field is required.')
    })
    .min(8, t('Must be at least 8 characters'))
})

type PasswordSchema = z.output<typeof passwordSchema>

const password = reactive<Partial<PasswordSchema>>({
  current_password: undefined,
  new_password: undefined,
  confirm_password: undefined
})

const validate = (state: Partial<PasswordSchema>): FormError[] => {
  const errors: FormError[] = []
  if (
    state.current_password
    && state.new_password
    && state.current_password === state.new_password
  ) {
    errors.push({
      name: 'new_password',
      message: t('Passwords must be different')
    })
  }

  if (
    state.new_password
    && state.confirm_password
    && state.new_password !== state.confirm_password
  ) {
    errors.push({
      name: 'confirm_password',
      message: t('Passwords must match')
    })
  }
  return errors
}

async function onSubmitChangePassword(
  payload: FormSubmitEvent<PasswordSchema>
) {
  try {
    const result = await authStore.changePassword({
      password: payload.data.current_password,
      new_password: payload.data.new_password
    })

    if (result) {
      toast.add({
        title: t('profile.passwordChanged') || 'Password Changed',
        description:
          t('profile.passwordChangedDescription')
          || 'Your password has been changed successfully',
        color: 'success'
      })

      // Clear form
      password.current_password = ''
      password.new_password = ''
      password.confirm_password = ''
    } else {
      // set error to current_password
    }
  } catch (error) {
    console.error('Password change error:', error)
    toast.add({
      title: t('profile.passwordChangeError') || 'Password Change Failed',
      description:
        error.message
        || t('profile.passwordChangeErrorDescription')
        || 'There was an error changing your password',
      color: 'error'
    })
  }
}

async function deleteAccount() {
  confirm.openConfirm({
    title: t('profile.deleteAccount') || 'Delete Account',
    description:
      t('profile.deleteAccountConfirmation')
      || 'Are you sure you want to delete your account? This action cannot be undone and all your data will be permanently lost.',
    icon: 'i-lucide-trash-2',
    confirmText: t('delete') || 'Delete',
    cancelText: t('cancel') || 'Cancel',
    onConfirm: async () => {
      try {
        const result = await authStore.deleteAccount()
        if (result) {
          toast.add({
            title: t('profile.accountDeleted') || 'Account Deleted',
            description:
              t('profile.accountDeletedDescription')
              || 'Your account has been deleted successfully',
            color: 'info'
          })

          // Log out user after account deletion
          setTimeout(() => {
            authStore.logout()
          }, 2000)
        } else {
          toast.add({
            title:
              t('profile.accountDeletionError') || 'Account Deletion Failed',
            description:
              t('profile.accountDeletionErrorDescription')
              || 'There was an error deleting your account',
            color: 'error'
          })
        }
      } catch (error) {
        console.error('Account deletion error:', error)
        toast.add({
          title: t('profile.accountDeletionError') || 'Account Deletion Failed',
          description:
            error.message
            || t('profile.accountDeletionErrorDescription')
            || 'There was an error deleting your account',
          color: 'error'
        })
      }
    }
  })
}
</script>

<template>
  <div>
    <div class="sm:max-w-2xl max-w-full mx-auto">
      <div class="space-y-10">
        <UPageCard
          :title="$t('Change Password')"
          :description="
            $t('Confirm your current password before setting a new one.')
          "
          variant="subtle"
        >
          <UForm
            :schema="passwordSchema"
            :state="password"
            :validate="validate"
            class="flex flex-col gap-4 max-w-xs"
            @submit="onSubmitChangePassword"
          >
            <UFormField
              name="current_password"
              :error="
                error
                  && $t(
                    error?.error_code
                      || 'Something went wrong. Please try again.'
                  )
              "
            >
              <UInput
                v-model="password.current_password"
                type="password"
                :placeholder="$t('Current password')"
                class="w-full"
              />
            </UFormField>

            <UFormField name="new_password">
              <UInput
                v-model="password.new_password"
                type="password"
                :placeholder="$t('New password')"
                class="w-full"
              />
            </UFormField>

            <UFormField name="confirm_password">
              <UInput
                v-model="password.confirm_password"
                type="password"
                :placeholder="$t('Confirm new password')"
                class="w-full"
              />
            </UFormField>

            <UButton
              :label="$t('Update')"
              class="w-fit"
              type="submit"
              :loading="loading"
            />
          </UForm>
        </UPageCard>

        <UPageCard
          :title="$t('Account')"
          :description="
            $t(
              'No longer want to use our service? You can delete your account here. This action is not reversible. All information related to this account will be deleted permanently.'
            )
          "
          class="bg-gradient-to-tl from-error/10 from-5% to-default"
        >
          <template #footer>
            <UButton
              :label="$t('Delete account')"
              color="error"
              :loading="loading"
              @click="deleteAccount"
            />
          </template>
        </UPageCard>
      </div>
    </div>
  </div>
</template>
