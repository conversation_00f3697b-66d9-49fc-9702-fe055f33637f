<script setup lang="ts">
const { t } = useI18n()

useSeoMeta({
  title: `${t('terms.title')} - Imagen`,
  description: t('terms.description'),
  ogTitle: `${t('terms.title')} - Imagen`,
  ogDescription: t('terms.description')
})
</script>

<template>
  <UPage>
    <UContainer>
      <UPageHeader
        :title="$t('terms.title')"
        :description="$t('terms.description')"
      />
    </UContainer>

    <UContainer class="mt-4">
      <div class="prose dark:prose-invert max-w-none">
        <h2>{{ $t("terms.acceptanceOfTerms") }}</h2>
        <p>
          {{ $t("terms.acceptanceOfTermsDescription") }}
        </p>

        <h2>{{ $t("terms.useOfService") }}</h2>
        <p>
          {{ $t("terms.useOfServiceDescription") }}
        </p>

        <h2>{{ $t("terms.userAccounts") }}</h2>
        <p>
          {{ $t("terms.userAccountsDescription") }}
        </p>

        <h2>{{ $t("terms.intellectualProperty") }}</h2>
        <p>
          {{ $t("terms.intellectualPropertyDescription") }}
        </p>

        <h2>{{ $t("terms.termination") }}</h2>
        <p>
          {{ $t("terms.terminationDescription") }}
        </p>

        <h2>{{ $t("terms.disclaimers") }}</h2>
        <p>
          {{ $t("terms.disclaimersDescription") }}
        </p>

        <h2>{{ $t("terms.contactUsTerms") }}</h2>
        <p>
          {{ $t("terms.contactUsTermsDescription") }}
        </p>
      </div>
    </UContainer>
  </UPage>
</template>
