import { defineStore } from 'pinia'

export const useAppStore = defineStore('appStore', {
  state: () => ({
    locale: 'en',
    loading: false,
    isNotificationsSlideoverOpen: false,

    adsBanner: {
      id: '1',
      icon: 'hugeicons:promotion',
      title: 'To celebrate our launch, enjoy 50% off select Gemini API models. Offer valid until further notice.'
    }
  }),
  persist: [
    {
      pick: ['locale'],
      storage: localStorage
    }
  ],
  getters: {
    localeForI18n: (state: any) => {
      return state.locale.replace('-', '_').toLowerCase()
    },
    hasHeaderBanner(): boolean {
      const authStore = useAuthStore()

      return !!authStore.isNotVerifyAccount || this.adsBanner
    }
  }
})
