import { describe, it, expect } from 'vitest'
import { 
  deepEqual, 
  compareImageArrays, 
  compareFileArrays, 
  compareDialogArrays,
  commonValidationRules,
  runValidations
} from '~/utils/generationValidation'

describe('generationValidation', () => {
  describe('deepEqual', () => {
    it('should return true for identical primitives', () => {
      expect(deepEqual(1, 1)).toBe(true)
      expect(deepEqual('test', 'test')).toBe(true)
      expect(deepEqual(true, true)).toBe(true)
      expect(deepEqual(null, null)).toBe(true)
      expect(deepEqual(undefined, undefined)).toBe(true)
    })

    it('should return false for different primitives', () => {
      expect(deepEqual(1, 2)).toBe(false)
      expect(deepEqual('test', 'test2')).toBe(false)
      expect(deepEqual(true, false)).toBe(false)
      expect(deepEqual(null, undefined)).toBe(false)
    })

    it('should handle arrays correctly', () => {
      expect(deepEqual([1, 2, 3], [1, 2, 3])).toBe(true)
      expect(deepEqual([1, 2, 3], [1, 2, 4])).toBe(false)
      expect(deepEqual([1, 2], [1, 2, 3])).toBe(false)
    })

    it('should handle objects correctly', () => {
      expect(deepEqual({ a: 1, b: 2 }, { a: 1, b: 2 })).toBe(true)
      expect(deepEqual({ a: 1, b: 2 }, { a: 1, b: 3 })).toBe(false)
      expect(deepEqual({ a: 1 }, { a: 1, b: 2 })).toBe(false)
    })
  })

  describe('compareImageArrays', () => {
    it('should detect no changes for identical arrays', () => {
      const images = [
        { src: 'image1.jpg', alt: 'Image 1' },
        { src: 'image2.jpg', alt: 'Image 2' }
      ]
      expect(compareImageArrays(images, images)).toBe(false)
    })

    it('should detect changes in length', () => {
      const images1 = [{ src: 'image1.jpg', alt: 'Image 1' }]
      const images2 = [
        { src: 'image1.jpg', alt: 'Image 1' },
        { src: 'image2.jpg', alt: 'Image 2' }
      ]
      expect(compareImageArrays(images1, images2)).toBe(true)
    })

    it('should detect changes in src', () => {
      const images1 = [{ src: 'image1.jpg', alt: 'Image 1' }]
      const images2 = [{ src: 'image2.jpg', alt: 'Image 1' }]
      expect(compareImageArrays(images1, images2)).toBe(true)
    })

    it('should detect changes in alt', () => {
      const images1 = [{ src: 'image1.jpg', alt: 'Image 1' }]
      const images2 = [{ src: 'image1.jpg', alt: 'Image 2' }]
      expect(compareImageArrays(images1, images2)).toBe(true)
    })
  })

  describe('compareFileArrays', () => {
    it('should detect no changes for identical arrays', () => {
      const files = [
        { name: 'file1.txt', size: 100 },
        { name: 'file2.txt', size: 200 }
      ]
      expect(compareFileArrays(files, files)).toBe(false)
    })

    it('should detect changes in name', () => {
      const files1 = [{ name: 'file1.txt', size: 100 }]
      const files2 = [{ name: 'file2.txt', size: 100 }]
      expect(compareFileArrays(files1, files2)).toBe(true)
    })

    it('should detect changes in size', () => {
      const files1 = [{ name: 'file1.txt', size: 100 }]
      const files2 = [{ name: 'file1.txt', size: 200 }]
      expect(compareFileArrays(files1, files2)).toBe(true)
    })
  })

  describe('compareDialogArrays', () => {
    it('should detect no changes for identical arrays', () => {
      const dialogs = [
        { speaker: 'A', text: 'Hello', voice_id: 'voice1' },
        { speaker: 'B', text: 'Hi', voice_id: 'voice2' }
      ]
      expect(compareDialogArrays(dialogs, dialogs)).toBe(false)
    })

    it('should detect changes in speaker', () => {
      const dialogs1 = [{ speaker: 'A', text: 'Hello', voice_id: 'voice1' }]
      const dialogs2 = [{ speaker: 'B', text: 'Hello', voice_id: 'voice1' }]
      expect(compareDialogArrays(dialogs1, dialogs2)).toBe(true)
    })

    it('should detect changes in text', () => {
      const dialogs1 = [{ speaker: 'A', text: 'Hello', voice_id: 'voice1' }]
      const dialogs2 = [{ speaker: 'A', text: 'Hi', voice_id: 'voice1' }]
      expect(compareDialogArrays(dialogs1, dialogs2)).toBe(true)
    })

    it('should detect changes in voice_id', () => {
      const dialogs1 = [{ speaker: 'A', text: 'Hello', voice_id: 'voice1' }]
      const dialogs2 = [{ speaker: 'A', text: 'Hello', voice_id: 'voice2' }]
      expect(compareDialogArrays(dialogs1, dialogs2)).toBe(true)
    })
  })

  describe('commonValidationRules', () => {
    it('should validate required text correctly', () => {
      const rule1 = commonValidationRules.requiredText('hello', 'Required')
      const rule2 = commonValidationRules.requiredText('', 'Required')
      const rule3 = commonValidationRules.requiredText('   ', 'Required')

      expect(rule1().isValid).toBe(true)
      expect(rule2().isValid).toBe(false)
      expect(rule3().isValid).toBe(false)
      expect(rule2().message).toBe('Required')
    })

    it('should validate required value correctly', () => {
      const rule1 = commonValidationRules.requiredValue('value', 'Required')
      const rule2 = commonValidationRules.requiredValue(null, 'Required')
      const rule3 = commonValidationRules.requiredValue(undefined, 'Required')

      expect(rule1().isValid).toBe(true)
      expect(rule2().isValid).toBe(false)
      expect(rule3().isValid).toBe(false)
    })

    it('should validate required array correctly', () => {
      const rule1 = commonValidationRules.requiredArray([1, 2, 3], 'Required')
      const rule2 = commonValidationRules.requiredArray([], 'Required')
      const rule3 = commonValidationRules.requiredArray(null as any, 'Required')

      expect(rule1().isValid).toBe(true)
      expect(rule2().isValid).toBe(false)
      expect(rule3().isValid).toBe(false)
    })

    it('should validate required multiple correctly', () => {
      const rule1 = commonValidationRules.requiredMultiple(['a', 'b'], 'Required')
      const rule2 = commonValidationRules.requiredMultiple(['a', null], 'Required')
      const rule3 = commonValidationRules.requiredMultiple([null, undefined], 'Required')

      expect(rule1().isValid).toBe(true)
      expect(rule2().isValid).toBe(false)
      expect(rule3().isValid).toBe(false)
    })
  })

  describe('runValidations', () => {
    it('should return success for all valid rules', () => {
      const rules = [
        () => ({ isValid: true }),
        () => ({ isValid: true }),
        () => ({ isValid: true })
      ]

      const result = runValidations(rules)
      expect(result.isValid).toBe(true)
    })

    it('should return first invalid rule', () => {
      const rules = [
        () => ({ isValid: true }),
        () => ({ isValid: false, message: 'Error 1' }),
        () => ({ isValid: false, message: 'Error 2' })
      ]

      const result = runValidations(rules)
      expect(result.isValid).toBe(false)
      expect(result.message).toBe('Error 1')
    })

    it('should handle empty rules array', () => {
      const result = runValidations([])
      expect(result.isValid).toBe(true)
    })
  })
})
