---
title: Authentication
description: Learn how to authenticate with the Image Generation API using API keys.
navigation:
  icon: i-lucide-key
---

## API Key Authentication

The Image Generation API uses API key authentication. You'll need to include your API key in the `Authorization` header of every request.

## Getting Your API Key

1. **Sign up** for an account at [imagen.ai](https://imagen.ai)
2. **Navigate** to your [API Keys dashboard](https://imagen.ai/dashboard/api-keys)
3. **Create** a new API key by clicking "Generate New Key"
4. **Copy** your API key and store it securely

::alert{type="warning"}
Keep your API key secure and never expose it in client-side code. Always use it from your backend or server-side applications.
::

## Using Your API Key

Include your API key in the `Authorization` header with the `Bearer` prefix:

```bash
curl -X POST https://api.imagen.ai/v1/text-to-image \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "prompt": "A beautiful sunset over mountains",
    "model": "imagen-v2"
  }'
```

## Environment Variables

For security, store your API key as an environment variable:

::code-group

```bash [.env]
IMAGEN_API_KEY=your_api_key_here
```

```javascript [Node.js]
const apiKey = process.env.IMAGEN_API_KEY;

const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': `Bearer ${apiKey}`
  },
  body: JSON.stringify({
    prompt: 'A beautiful sunset over mountains',
    model: 'imagen-v2'
  })
});
```

```python [Python]
import os
import requests

api_key = os.getenv('IMAGEN_API_KEY')

response = requests.post(
    'https://api.imagen.ai/v1/text-to-image',
    headers={
        'Content-Type': 'application/json',
        'Authorization': f'Bearer {api_key}'
    },
    json={
        'prompt': 'A beautiful sunset over mountains',
        'model': 'imagen-v2'
    }
)
```

::

## API Key Management

### Best Practices

- **Rotate keys regularly** for enhanced security
- **Use different keys** for development and production environments
- **Monitor usage** through the dashboard to detect unusual activity
- **Set up alerts** for usage limits and quota warnings

### Key Permissions

API keys can have different permission levels:

- **Read**: View account information and usage statistics
- **Generate**: Create images using the API
- **Admin**: Full access including billing and key management

### Rate Limits

Each API key has rate limits based on your subscription plan:

| Plan | Requests per minute | Requests per day |
|------|-------------------|------------------|
| Free | 10 | 100 |
| Pro | 100 | 10,000 |
| Enterprise | 1,000 | 100,000 |

## Error Handling

Common authentication errors and how to handle them:

### 401 Unauthorized

```json
{
  "error": {
    "code": "unauthorized",
    "message": "Invalid API key"
  }
}
```

**Solution**: Check that your API key is correct and properly formatted in the Authorization header.

### 403 Forbidden

```json
{
  "error": {
    "code": "forbidden",
    "message": "API key does not have permission for this operation"
  }
}
```

**Solution**: Ensure your API key has the necessary permissions for the endpoint you're trying to access.

### 429 Too Many Requests

```json
{
  "error": {
    "code": "rate_limit_exceeded",
    "message": "Rate limit exceeded. Please try again later."
  }
}
```

**Solution**: Implement exponential backoff in your retry logic or upgrade your plan for higher limits.

## Next Steps

- [Make your first request](/getting-started/quickstart)
- [Explore the API reference](/api-reference)
- [View code examples](/examples)
