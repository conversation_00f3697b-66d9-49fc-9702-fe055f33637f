---
title: Quickstart
description: Generate your first image in minutes with the Image Generation API.
navigation:
  icon: i-lucide-zap
---

Get up and running with the Image Generation API in just a few minutes. This guide will walk you through making your first API request.

## Prerequisites

Before you start, make sure you have:

1. **An API key** - [Get your free API key](/getting-started/authentication)
2. **A development environment** - Terminal, code editor, or API testing tool

## Your First Request

Let's generate your first image using a simple text prompt:

### Using cURL

```bash
curl -X POST https://api.imagen.ai/v1/text-to-image \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "prompt": "A serene mountain landscape at sunset with a crystal clear lake",
    "model": "imagen-v2",
    "width": 1024,
    "height": 1024,
    "steps": 30
  }'
```

### Response

You'll receive a response like this:

```json
{
  "id": "img_abc123",
  "status": "completed",
  "prompt": "A serene mountain landscape at sunset with a crystal clear lake",
  "model": "imagen-v2",
  "width": 1024,
  "height": 1024,
  "steps": 30,
  "created_at": "2024-01-15T10:30:00Z",
  "images": [
    {
      "url": "https://cdn.imagen.ai/images/img_abc123_0.png",
      "width": 1024,
      "height": 1024
    }
  ]
}
```

## Code Examples

Here are examples in popular programming languages:

::code-group

```javascript [Node.js]
const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    prompt: 'A serene mountain landscape at sunset with a crystal clear lake',
    model: 'imagen-v2',
    width: 1024,
    height: 1024,
    steps: 30
  })
});

const result = await response.json();
console.log('Generated image:', result.images[0].url);
```

```python [Python]
import requests

response = requests.post(
    'https://api.imagen.ai/v1/text-to-image',
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
    },
    json={
        'prompt': 'A serene mountain landscape at sunset with a crystal clear lake',
        'model': 'imagen-v2',
        'width': 1024,
        'height': 1024,
        'steps': 30
    }
)

result = response.json()
print(f"Generated image: {result['images'][0]['url']}")
```

::

## Understanding the Parameters

Let's break down the key parameters:

- **`prompt`** - The text description of the image you want to generate
- **`model`** - The AI model to use (e.g., `imagen-v2`, `imagen-v3`)
- **`width`** & **`height`** - Image dimensions in pixels
- **`steps`** - Number of inference steps (higher = better quality, slower)

## Common Use Cases

### Basic Text-to-Image

```json
{
  "prompt": "A cute golden retriever puppy playing in a garden",
  "model": "imagen-v2"
}
```

### High-Quality Art

```json
{
  "prompt": "Digital art of a cyberpunk cityscape at night, neon lights, highly detailed",
  "model": "imagen-v3",
  "width": 1920,
  "height": 1080,
  "steps": 50
}
```

## Next Steps

Now that you've generated your first image, explore more advanced features:

- **[Text-to-Image API](/api-reference/text-to-image)** - Learn about all available parameters
- **[Image-to-Image API](/api-reference/image-to-image)** - Transform existing images
- **[Models](/api-reference/models)** - Explore different AI models
- **[Examples](/examples)** - See real-world implementations

::alert{type="info"}
**Tip**: Start with simple prompts and gradually add more details to get better results. The AI works best with clear, descriptive language.
::
