---
title: API Reference
description: Complete reference for the Image Generation API endpoints and parameters.
navigation:
  icon: i-lucide-book
---

# API Reference

The Image Generation API provides powerful endpoints for creating and transforming images using state-of-the-art AI models.

## Base URL

All API requests should be made to:

```
https://api.imagen.ai/v1
```

## Authentication

All requests require authentication using an API key in the Authorization header:

```bash
Authorization: Bearer YOUR_API_KEY
```

## Content Type

All requests should include the Content-Type header:

```bash
Content-Type: application/json
```

## Available Endpoints

### Text to Image
Generate images from text prompts.

```bash
POST /text-to-image
```

[Learn more →](/api-reference/text-to-image)

### Image to Image
Transform existing images with AI.

```bash
POST /image-to-image
```

[Learn more →](/api-reference/image-to-image)

### Image Variations
Create variations of existing images.

```bash
POST /variations
```

[Learn more →](/api-reference/variations)

### Models
List available AI models.

```bash
GET /models
```

[Learn more →](/api-reference/models)

## Response Format

All successful responses return JSO<PERSON> with the following structure:

```json
{
  "id": "string",
  "status": "completed" | "processing" | "failed",
  "created_at": "2024-01-15T10:30:00Z",
  "model": "string",
  "prompt": "string",
  "images": [
    {
      "url": "string",
      "width": 1024,
      "height": 1024
    }
  ]
}
```

## Error Handling

Errors are returned with appropriate HTTP status codes and a JSON response:

```json
{
  "error": {
    "code": "error_code",
    "message": "Human readable error message",
    "details": "Additional error details (optional)"
  }
}
```

### Common Error Codes

| Status Code | Error Code | Description |
|-------------|------------|-------------|
| 400 | `bad_request` | Invalid request parameters |
| 401 | `unauthorized` | Invalid or missing API key |
| 403 | `forbidden` | Insufficient permissions |
| 429 | `rate_limit_exceeded` | Too many requests |
| 500 | `internal_error` | Server error |

## Rate Limits

Rate limits vary by subscription plan:

| Plan | Requests per minute | Requests per day |
|------|-------------------|------------------|
| Free | 10 | 100 |
| Pro | 100 | 10,000 |
| Enterprise | 1,000 | 100,000 |

Rate limit information is included in response headers:

```bash
X-RateLimit-Limit: 100
X-RateLimit-Remaining: 95
X-RateLimit-Reset: 1642694400
```

## SDKs and Libraries

Official SDKs are available for popular programming languages:

- **Node.js**: `npm install @imagen/node`
- **Python**: `pip install imagen-python`
- **PHP**: `composer require imagen/php-sdk`
- **Go**: `go get github.com/imagen/go-sdk`

## Webhooks

For long-running image generation tasks, you can use webhooks to receive notifications when processing is complete:

```json
{
  "webhook_url": "https://your-app.com/webhook",
  "webhook_secret": "your_webhook_secret"
}
```

[Learn more about webhooks →](/guides/webhooks)

## Next Steps

- [Text to Image API →](/api-reference/text-to-image)
- [Image to Image API →](/api-reference/image-to-image)
- [Available Models →](/api-reference/models)
- [Code Examples →](/examples)
