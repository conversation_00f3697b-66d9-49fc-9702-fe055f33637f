---
title: Text to Image
description: Generate stunning images from text prompts using AI models.
navigation:
  icon: i-lucide-type
---

# Text to Image API

Generate high-quality images from text descriptions using state-of-the-art AI models.

## Endpoint

```bash
POST https://api.imagen.ai/v1/text-to-image
```

## Request Body

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `prompt` | string | Yes | Text description of the image to generate |
| `model` | string | No | AI model to use (default: `imagen-v2`) |
| `width` | integer | No | Image width in pixels (default: 1024) |
| `height` | integer | No | Image height in pixels (default: 1024) |
| `steps` | integer | No | Number of inference steps (default: 30) |
| `guidance_scale` | float | No | How closely to follow the prompt (default: 7.5) |
| `negative_prompt` | string | No | What to avoid in the image |
| `seed` | integer | No | Random seed for reproducible results |
| `num_images` | integer | No | Number of images to generate (1-4, default: 1) |
| `style` | string | No | Artistic style to apply |
| `webhook_url` | string | No | URL to receive completion notification |

## Example Request

```bash
curl -X POST https://api.imagen.ai/v1/text-to-image \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "prompt": "A majestic dragon flying over a medieval castle at sunset, fantasy art style",
    "model": "imagen-v3",
    "width": 1920,
    "height": 1080,
    "steps": 50,
    "guidance_scale": 8.0,
    "negative_prompt": "blurry, low quality, distorted",
    "num_images": 2,
    "style": "fantasy"
  }'
```

## Response

### Successful Response (200 OK)

```json
{
  "id": "img_abc123def456",
  "status": "completed",
  "prompt": "A majestic dragon flying over a medieval castle at sunset, fantasy art style",
  "negative_prompt": "blurry, low quality, distorted",
  "model": "imagen-v3",
  "width": 1920,
  "height": 1080,
  "steps": 50,
  "guidance_scale": 8.0,
  "seed": 42,
  "style": "fantasy",
  "created_at": "2024-01-15T10:30:00Z",
  "completed_at": "2024-01-15T10:30:15Z",
  "images": [
    {
      "url": "https://cdn.imagen.ai/images/img_abc123def456_0.png",
      "width": 1920,
      "height": 1080
    },
    {
      "url": "https://cdn.imagen.ai/images/img_abc123def456_1.png", 
      "width": 1920,
      "height": 1080
    }
  ]
}
```

## Parameters in Detail

### prompt
The text description of the image you want to generate. Be descriptive and specific for better results.

**Examples:**
- `"A serene mountain landscape at sunset"`
- `"Portrait of a wise old wizard with a long beard, digital art"`
- `"Modern minimalist living room with natural lighting"`

### model
Choose from available AI models optimized for different use cases:

- `imagen-v2` - Balanced quality and speed (default)
- `imagen-v3` - Highest quality, slower generation
- `imagen-fast` - Fastest generation, good quality
- `imagen-artistic` - Optimized for artistic styles

### width & height
Supported resolutions:

| Aspect Ratio | Dimensions |
|--------------|------------|
| Square | 512x512, 1024x1024, 2048x2048 |
| Portrait | 768x1024, 1024x1536 |
| Landscape | 1024x768, 1536x1024, 1920x1080 |

### steps
Number of inference steps. Higher values generally produce better quality but take longer:

- `20-30` - Fast generation, good quality
- `30-50` - Balanced quality and speed
- `50-100` - High quality, slower generation

### guidance_scale
Controls how closely the AI follows your prompt:

- `1.0-5.0` - More creative, less adherence to prompt
- `7.5` - Balanced (recommended)
- `10.0-20.0` - Strict adherence to prompt

### negative_prompt
Specify what you don't want in the image:

**Common negative prompts:**
- `"blurry, low quality, distorted"`
- `"text, watermark, signature"`
- `"extra limbs, deformed hands"`

### seed
Use the same seed to reproduce identical results with the same parameters.

### style
Apply predefined artistic styles:

- `realistic` - Photorealistic images
- `artistic` - Painterly, artistic style
- `anime` - Anime/manga style
- `fantasy` - Fantasy art style
- `cyberpunk` - Futuristic cyberpunk aesthetic
- `vintage` - Retro/vintage look

## Code Examples

::code-group

```javascript [Node.js]
const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    prompt: 'A majestic dragon flying over a medieval castle',
    model: 'imagen-v3',
    width: 1920,
    height: 1080,
    steps: 50
  })
});

const result = await response.json();
console.log('Generated images:', result.images);
```

```python [Python]
import requests

response = requests.post(
    'https://api.imagen.ai/v1/text-to-image',
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
    },
    json={
        'prompt': 'A majestic dragon flying over a medieval castle',
        'model': 'imagen-v3',
        'width': 1920,
        'height': 1080,
        'steps': 50
    }
)

result = response.json()
for i, image in enumerate(result['images']):
    print(f"Image {i+1}: {image['url']}")
```

```php [PHP]
<?php
$response = file_get_contents('https://api.imagen.ai/v1/text-to-image', false, stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Authorization: Bearer YOUR_API_KEY'
        ],
        'content' => json_encode([
            'prompt' => 'A majestic dragon flying over a medieval castle',
            'model' => 'imagen-v3',
            'width' => 1920,
            'height' => 1080,
            'steps' => 50
        ])
    ]
]));

$result = json_decode($response, true);
foreach ($result['images'] as $i => $image) {
    echo "Image " . ($i + 1) . ": " . $image['url'] . "\n";
}
?>
```

::

## Error Responses

### 400 Bad Request

```json
{
  "error": {
    "code": "invalid_parameters",
    "message": "Invalid image dimensions",
    "details": "Width and height must be between 256 and 2048 pixels"
  }
}
```

### 402 Payment Required

```json
{
  "error": {
    "code": "insufficient_credits",
    "message": "Insufficient credits to complete request",
    "details": "This request requires 5 credits, but you have 2 remaining"
  }
}
```

## Best Practices

1. **Be specific in prompts** - Include details about style, lighting, composition
2. **Use negative prompts** - Exclude unwanted elements for better results
3. **Start with lower steps** - Test with 30 steps before using higher values
4. **Choose appropriate models** - Use `imagen-fast` for prototyping, `imagen-v3` for final images
5. **Handle errors gracefully** - Implement retry logic with exponential backoff

## Next Steps

- [Image to Image API →](/api-reference/image-to-image)
- [Available Models →](/api-reference/models)
- [Code Examples →](/examples/text-to-image)
