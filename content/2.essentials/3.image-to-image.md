---
title: Image to Image
description: Transform existing images with AI-powered modifications and style transfers.
navigation:
  icon: i-lucide-image
---

# Image to Image API

Transform existing images using AI models. Perfect for style transfer, modifications, and enhancements.

## Endpoint

```bash
POST https://api.imagen.ai/v1/image-to-image
```

## Request Body

| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `image` | string | Yes | Base64 encoded image or image URL |
| `prompt` | string | Yes | Description of desired transformation |
| `model` | string | No | AI model to use (default: `imagen-v2`) |
| `strength` | float | No | How much to transform the image (0.1-1.0, default: 0.7) |
| `steps` | integer | No | Number of inference steps (default: 30) |
| `guidance_scale` | float | No | How closely to follow the prompt (default: 7.5) |
| `negative_prompt` | string | No | What to avoid in the transformation |
| `seed` | integer | No | Random seed for reproducible results |
| `num_images` | integer | No | Number of variations to generate (1-4, default: 1) |
| `style` | string | No | Artistic style to apply |
| `webhook_url` | string | No | URL to receive completion notification |

## Example Request

```bash
curl -X POST https://api.imagen.ai/v1/image-to-image \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer YOUR_API_KEY" \
  -d '{
    "image": "https://example.com/input-image.jpg",
    "prompt": "Transform this into a cyberpunk style with neon lights and futuristic elements",
    "model": "imagen-v3",
    "strength": 0.8,
    "steps": 50,
    "guidance_scale": 8.0,
    "negative_prompt": "blurry, low quality",
    "style": "cyberpunk",
    "num_images": 2
  }'
```

## Response

### Successful Response (200 OK)

```json
{
  "id": "img_transform_abc123",
  "status": "completed",
  "prompt": "Transform this into a cyberpunk style with neon lights and futuristic elements",
  "negative_prompt": "blurry, low quality",
  "model": "imagen-v3",
  "strength": 0.8,
  "steps": 50,
  "guidance_scale": 8.0,
  "seed": 42,
  "style": "cyberpunk",
  "created_at": "2024-01-15T10:30:00Z",
  "completed_at": "2024-01-15T10:30:20Z",
  "original_image": {
    "url": "https://example.com/input-image.jpg",
    "width": 1024,
    "height": 1024
  },
  "images": [
    {
      "url": "https://cdn.imagen.ai/images/img_transform_abc123_0.png",
      "width": 1024,
      "height": 1024
    },
    {
      "url": "https://cdn.imagen.ai/images/img_transform_abc123_1.png",
      "width": 1024,
      "height": 1024
    }
  ]
}
```

## Parameters in Detail

### image
Provide the source image in one of these formats:

**Image URL:**
```json
{
  "image": "https://example.com/image.jpg"
}
```

**Base64 encoded:**
```json
{
  "image": "data:image/jpeg;base64,/9j/4AAQSkZJRgABAQAAAQABAAD..."
}
```

**Supported formats:** JPEG, PNG, WebP
**Maximum file size:** 10MB
**Maximum dimensions:** 2048x2048

### prompt
Describe the transformation you want to apply:

**Style Transfer Examples:**
- `"Transform into an oil painting style"`
- `"Make it look like a watercolor artwork"`
- `"Convert to anime/manga style"`

**Modification Examples:**
- `"Add dramatic lighting and shadows"`
- `"Change the time of day to sunset"`
- `"Add snow and winter atmosphere"`

### strength
Controls how much the original image is transformed:

- `0.1-0.3` - Subtle changes, preserves original structure
- `0.4-0.7` - Moderate transformation (recommended)
- `0.8-1.0` - Heavy transformation, may lose original details

### Common Use Cases

## Style Transfer

Transform images into different artistic styles:

```json
{
  "image": "https://example.com/portrait.jpg",
  "prompt": "Transform into Van Gogh painting style with swirling brushstrokes",
  "strength": 0.6,
  "style": "artistic"
}
```

## Photo Enhancement

Improve and enhance existing photos:

```json
{
  "image": "https://example.com/old-photo.jpg", 
  "prompt": "Enhance quality, improve lighting, remove noise, make colors more vibrant",
  "strength": 0.4,
  "steps": 40
}
```

## Scene Modification

Change elements within the image:

```json
{
  "image": "https://example.com/landscape.jpg",
  "prompt": "Change from day to night, add stars and moonlight",
  "strength": 0.7,
  "negative_prompt": "sun, daylight, bright"
}
```

## Fashion and Product

Transform product images:

```json
{
  "image": "https://example.com/product.jpg",
  "prompt": "Place product in luxury setting with elegant background",
  "strength": 0.5,
  "style": "realistic"
}
```

## Code Examples

::code-group

```javascript [Node.js]
const fs = require('fs');

// Using image URL
const response = await fetch('https://api.imagen.ai/v1/image-to-image', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    image: 'https://example.com/input.jpg',
    prompt: 'Transform into cyberpunk style',
    strength: 0.7,
    steps: 40
  })
});

const result = await response.json();
console.log('Transformed images:', result.images);
```

```python [Python]
import requests
import base64

# Using base64 encoded image
with open('input.jpg', 'rb') as f:
    image_data = base64.b64encode(f.read()).decode()

response = requests.post(
    'https://api.imagen.ai/v1/image-to-image',
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
    },
    json={
        'image': f'data:image/jpeg;base64,{image_data}',
        'prompt': 'Transform into cyberpunk style',
        'strength': 0.7,
        'steps': 40
    }
)

result = response.json()
for i, image in enumerate(result['images']):
    print(f"Transformed image {i+1}: {image['url']}")
```

```php [PHP]
<?php
// Using image URL
$data = [
    'image' => 'https://example.com/input.jpg',
    'prompt' => 'Transform into cyberpunk style',
    'strength' => 0.7,
    'steps' => 40
];

$response = file_get_contents('https://api.imagen.ai/v1/image-to-image', false, stream_context_create([
    'http' => [
        'method' => 'POST',
        'header' => [
            'Content-Type: application/json',
            'Authorization: Bearer YOUR_API_KEY'
        ],
        'content' => json_encode($data)
    ]
]));

$result = json_decode($response, true);
foreach ($result['images'] as $i => $image) {
    echo "Transformed image " . ($i + 1) . ": " . $image['url'] . "\n";
}
?>
```

::

## Error Responses

### 400 Bad Request - Invalid Image

```json
{
  "error": {
    "code": "invalid_image",
    "message": "Unable to process the provided image",
    "details": "Image format not supported or file corrupted"
  }
}
```

### 413 Payload Too Large

```json
{
  "error": {
    "code": "image_too_large",
    "message": "Image file size exceeds maximum limit",
    "details": "Maximum file size is 10MB"
  }
}
```

## Best Practices

1. **Choose appropriate strength** - Start with 0.5-0.7 for most transformations
2. **Use high-quality source images** - Better input leads to better output
3. **Be specific in prompts** - Describe exactly what transformation you want
4. **Test different models** - Some models work better for specific styles
5. **Use negative prompts** - Exclude unwanted artifacts or elements

## Limitations

- Source image must be under 10MB
- Maximum resolution: 2048x2048 pixels
- Processing time varies based on image size and complexity
- Some fine details may be lost during transformation

## Next Steps

- [Image Variations API →](/api-reference/variations)
- [Available Models →](/api-reference/models)
- [Code Examples →](/examples/image-to-image)
