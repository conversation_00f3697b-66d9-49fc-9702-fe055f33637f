---
title: Models
description: Explore available AI models for image generation and their capabilities.
navigation:
  icon: i-lucide-cpu
---

# Available Models

Choose from various AI models optimized for different use cases, styles, and performance requirements.

## List Models Endpoint

```bash
GET https://api.imagen.ai/v1/models
```

### Response

```json
{
  "models": [
    {
      "id": "imagen-v3",
      "name": "Imagen V3",
      "description": "Latest model with highest quality output",
      "type": "text-to-image",
      "max_resolution": "2048x2048",
      "avg_generation_time": "15-30s",
      "strengths": ["photorealism", "fine_details", "complex_scenes"],
      "pricing": {
        "cost_per_image": 0.05
      }
    },
    {
      "id": "imagen-v2",
      "name": "Imagen V2", 
      "description": "Balanced quality and speed",
      "type": "text-to-image",
      "max_resolution": "1536x1536",
      "avg_generation_time": "8-15s",
      "strengths": ["versatility", "speed", "consistency"],
      "pricing": {
        "cost_per_image": 0.03
      }
    }
  ]
}
```

## Model Comparison

### Imagen V3 (Latest)
**Best for:** High-quality final images, professional use

- **Strengths:** Photorealistic output, fine details, complex compositions
- **Max Resolution:** 2048x2048
- **Generation Time:** 15-30 seconds
- **Cost:** $0.05 per image
- **Use Cases:** Marketing materials, professional photography, detailed artwork

```json
{
  "model": "imagen-v3",
  "prompt": "Professional headshot of a business executive",
  "width": 1920,
  "height": 1080,
  "steps": 50
}
```

### Imagen V2 (Recommended)
**Best for:** General purpose, balanced quality and speed

- **Strengths:** Versatile, consistent results, good speed
- **Max Resolution:** 1536x1536  
- **Generation Time:** 8-15 seconds
- **Cost:** $0.03 per image
- **Use Cases:** Content creation, prototyping, social media

```json
{
  "model": "imagen-v2",
  "prompt": "Cozy coffee shop interior with warm lighting",
  "width": 1024,
  "height": 1024,
  "steps": 30
}
```

### Imagen Fast
**Best for:** Rapid prototyping, high-volume generation

- **Strengths:** Speed, cost-effective, good for iterations
- **Max Resolution:** 1024x1024
- **Generation Time:** 3-8 seconds
- **Cost:** $0.01 per image
- **Use Cases:** Concept art, thumbnails, rapid iteration

```json
{
  "model": "imagen-fast",
  "prompt": "Simple logo design for tech startup",
  "width": 512,
  "height": 512,
  "steps": 20
}
```

### Imagen Artistic
**Best for:** Creative and artistic styles

- **Strengths:** Artistic interpretation, unique styles, creative freedom
- **Max Resolution:** 1536x1536
- **Generation Time:** 10-20 seconds
- **Cost:** $0.04 per image
- **Use Cases:** Digital art, illustrations, creative projects

```json
{
  "model": "imagen-artistic",
  "prompt": "Abstract painting of emotions in vibrant colors",
  "style": "abstract",
  "width": 1024,
  "height": 1024,
  "steps": 40
}
```

## Specialized Models

### Imagen Portrait
**Optimized for:** Human portraits and faces

- **Strengths:** Facial features, skin texture, portrait lighting
- **Max Resolution:** 1024x1536 (portrait orientation)
- **Generation Time:** 12-18 seconds
- **Cost:** $0.04 per image

```json
{
  "model": "imagen-portrait",
  "prompt": "Professional headshot of a young woman, studio lighting",
  "width": 1024,
  "height": 1536
}
```

### Imagen Architecture
**Optimized for:** Buildings and architectural visualization

- **Strengths:** Structural accuracy, architectural details, perspective
- **Max Resolution:** 1920x1080
- **Generation Time:** 15-25 seconds
- **Cost:** $0.05 per image

```json
{
  "model": "imagen-architecture",
  "prompt": "Modern glass office building with clean lines",
  "width": 1920,
  "height": 1080
}
```

### Imagen Product
**Optimized for:** Product photography and e-commerce

- **Strengths:** Clean backgrounds, product focus, commercial quality
- **Max Resolution:** 1024x1024
- **Generation Time:** 10-15 seconds
- **Cost:** $0.04 per image

```json
{
  "model": "imagen-product",
  "prompt": "Luxury watch on white background, professional product photo",
  "width": 1024,
  "height": 1024
}
```

## Model Selection Guide

### By Use Case

| Use Case | Recommended Model | Reason |
|----------|------------------|---------|
| Marketing Materials | `imagen-v3` | Highest quality for professional use |
| Social Media Content | `imagen-v2` | Good balance of quality and speed |
| Concept Art | `imagen-fast` | Quick iterations and low cost |
| Digital Art | `imagen-artistic` | Optimized for creative styles |
| Product Photos | `imagen-product` | Specialized for e-commerce |
| Portraits | `imagen-portrait` | Optimized for human faces |
| Architecture | `imagen-architecture` | Best for buildings and structures |

### By Budget

| Budget Level | Model | Cost per Image |
|--------------|-------|----------------|
| Low | `imagen-fast` | $0.01 |
| Medium | `imagen-v2` | $0.03 |
| High | `imagen-v3` | $0.05 |

### By Speed Requirements

| Speed Need | Model | Generation Time |
|------------|-------|-----------------|
| Fastest | `imagen-fast` | 3-8 seconds |
| Balanced | `imagen-v2` | 8-15 seconds |
| Quality First | `imagen-v3` | 15-30 seconds |

## Model Capabilities

### Resolution Support

| Model | Max Resolution | Recommended Resolutions |
|-------|----------------|------------------------|
| `imagen-v3` | 2048x2048 | 1920x1080, 1536x1024, 2048x2048 |
| `imagen-v2` | 1536x1536 | 1024x1024, 1536x1024, 1024x1536 |
| `imagen-fast` | 1024x1024 | 512x512, 768x768, 1024x1024 |
| `imagen-artistic` | 1536x1536 | 1024x1024, 1536x1024 |

### Style Support

| Model | Supported Styles |
|-------|------------------|
| `imagen-v3` | All styles, photorealistic |
| `imagen-v2` | Most styles, versatile |
| `imagen-fast` | Basic styles |
| `imagen-artistic` | All artistic styles, creative |

## Code Examples

::code-group

```javascript [Node.js]
// Get available models
const modelsResponse = await fetch('https://api.imagen.ai/v1/models', {
  headers: {
    'Authorization': 'Bearer YOUR_API_KEY'
  }
});

const models = await modelsResponse.json();
console.log('Available models:', models);

// Use specific model
const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
    'Authorization': 'Bearer YOUR_API_KEY'
  },
  body: JSON.stringify({
    prompt: 'Beautiful landscape',
    model: 'imagen-v3', // Specify model
    width: 1920,
    height: 1080
  })
});
```

```python [Python]
import requests

# Get available models
models_response = requests.get(
    'https://api.imagen.ai/v1/models',
    headers={'Authorization': 'Bearer YOUR_API_KEY'}
)

models = models_response.json()
print('Available models:', models)

# Use specific model
response = requests.post(
    'https://api.imagen.ai/v1/text-to-image',
    headers={
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
    },
    json={
        'prompt': 'Beautiful landscape',
        'model': 'imagen-v3',  # Specify model
        'width': 1920,
        'height': 1080
    }
)
```

::

## Model Updates

Models are regularly updated to improve quality and capabilities. Check the `/models` endpoint for the latest available models and their specifications.

### Version History

- **v3.0** - Latest release with improved photorealism
- **v2.1** - Enhanced consistency and speed
- **v2.0** - Balanced quality and performance
- **v1.0** - Initial release

## Next Steps

- [Text to Image API →](/api-reference/text-to-image)
- [Image to Image API →](/api-reference/image-to-image)
- [Code Examples →](/examples)
