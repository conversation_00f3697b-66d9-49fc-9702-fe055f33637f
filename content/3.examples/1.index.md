---
title: Code Examples
description: Real-world examples and implementations using the Image Generation API.
navigation:
  icon: i-lucide-code
---

# Code Examples

Explore practical examples and implementations to help you integrate the Image Generation API into your applications.

## Quick Start Examples

### Basic Text-to-Image

Generate a simple image from a text prompt:

::code-group

```javascript [Node.js]
const generateImage = async (prompt) => {
  const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      prompt: prompt,
      model: 'imagen-v2',
      width: 1024,
      height: 1024
    })
  });

  const result = await response.json();
  return result.images[0].url;
};

// Usage
const imageUrl = await generateImage('A beautiful sunset over mountains');
console.log('Generated image:', imageUrl);
```

```python [Python]
import requests

def generate_image(prompt):
    response = requests.post(
        'https://api.imagen.ai/v1/text-to-image',
        headers={
            'Content-Type': 'application/json',
            'Authorization': 'Bearer YOUR_API_KEY'
        },
        json={
            'prompt': prompt,
            'model': 'imagen-v2',
            'width': 1024,
            'height': 1024
        }
    )
    
    result = response.json()
    return result['images'][0]['url']

# Usage
image_url = generate_image('A beautiful sunset over mountains')
print(f'Generated image: {image_url}')
```

```php [PHP]
<?php
function generateImage($prompt) {
    $data = [
        'prompt' => $prompt,
        'model' => 'imagen-v2',
        'width' => 1024,
        'height' => 1024
    ];

    $response = file_get_contents('https://api.imagen.ai/v1/text-to-image', false, stream_context_create([
        'http' => [
            'method' => 'POST',
            'header' => [
                'Content-Type: application/json',
                'Authorization: Bearer YOUR_API_KEY'
            ],
            'content' => json_encode($data)
        ]
    ]));

    $result = json_decode($response, true);
    return $result['images'][0]['url'];
}

// Usage
$imageUrl = generateImage('A beautiful sunset over mountains');
echo "Generated image: $imageUrl\n";
?>
```

::

### Image Transformation

Transform an existing image with AI:

::code-group

```javascript [Node.js]
const transformImage = async (imageUrl, prompt) => {
  const response = await fetch('https://api.imagen.ai/v1/image-to-image', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Authorization': 'Bearer YOUR_API_KEY'
    },
    body: JSON.stringify({
      image: imageUrl,
      prompt: prompt,
      strength: 0.7,
      model: 'imagen-v2'
    })
  });

  const result = await response.json();
  return result.images[0].url;
};

// Usage
const originalImage = 'https://example.com/photo.jpg';
const transformedUrl = await transformImage(originalImage, 'Transform into cyberpunk style');
console.log('Transformed image:', transformedUrl);
```

```python [Python]
def transform_image(image_url, prompt):
    response = requests.post(
        'https://api.imagen.ai/v1/image-to-image',
        headers={
            'Content-Type': 'application/json',
            'Authorization': 'Bearer YOUR_API_KEY'
        },
        json={
            'image': image_url,
            'prompt': prompt,
            'strength': 0.7,
            'model': 'imagen-v2'
        }
    )
    
    result = response.json()
    return result['images'][0]['url']

# Usage
original_image = 'https://example.com/photo.jpg'
transformed_url = transform_image(original_image, 'Transform into cyberpunk style')
print(f'Transformed image: {transformed_url}')
```

::

## Advanced Examples

### Batch Image Generation

Generate multiple images efficiently:

```javascript
const generateBatch = async (prompts) => {
  const promises = prompts.map(prompt => 
    fetch('https://api.imagen.ai/v1/text-to-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': 'Bearer YOUR_API_KEY'
      },
      body: JSON.stringify({
        prompt: prompt,
        model: 'imagen-fast', // Use fast model for batch processing
        num_images: 2
      })
    }).then(res => res.json())
  );

  const results = await Promise.all(promises);
  return results.flatMap(result => result.images);
};

// Usage
const prompts = [
  'A red sports car',
  'A blue mountain landscape', 
  'A green forest scene'
];

const allImages = await generateBatch(prompts);
console.log(`Generated ${allImages.length} images`);
```

### Error Handling and Retry Logic

Robust error handling with exponential backoff:

```javascript
const generateImageWithRetry = async (prompt, maxRetries = 3) => {
  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer YOUR_API_KEY'
        },
        body: JSON.stringify({
          prompt: prompt,
          model: 'imagen-v2'
        })
      });

      if (!response.ok) {
        if (response.status === 429) {
          // Rate limited, wait and retry
          const waitTime = Math.pow(2, attempt) * 1000; // Exponential backoff
          console.log(`Rate limited, waiting ${waitTime}ms before retry ${attempt}`);
          await new Promise(resolve => setTimeout(resolve, waitTime));
          continue;
        }
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }

      const result = await response.json();
      return result.images[0].url;

    } catch (error) {
      console.error(`Attempt ${attempt} failed:`, error.message);
      
      if (attempt === maxRetries) {
        throw new Error(`Failed to generate image after ${maxRetries} attempts`);
      }
      
      // Wait before retry
      const waitTime = Math.pow(2, attempt) * 1000;
      await new Promise(resolve => setTimeout(resolve, waitTime));
    }
  }
};
```

### Image Download and Storage

Download and save generated images:

::code-group

```javascript [Node.js]
const fs = require('fs');
const https = require('https');
const path = require('path');

const downloadImage = (url, filename) => {
  return new Promise((resolve, reject) => {
    const file = fs.createWriteStream(filename);
    
    https.get(url, (response) => {
      response.pipe(file);
      
      file.on('finish', () => {
        file.close();
        resolve(filename);
      });
      
      file.on('error', (err) => {
        fs.unlink(filename, () => {}); // Delete partial file
        reject(err);
      });
    });
  });
};

// Generate and download image
const generateAndSave = async (prompt, filename) => {
  try {
    const imageUrl = await generateImage(prompt);
    const savedPath = await downloadImage(imageUrl, filename);
    console.log(`Image saved to: ${savedPath}`);
    return savedPath;
  } catch (error) {
    console.error('Error generating/saving image:', error);
    throw error;
  }
};

// Usage
await generateAndSave('A beautiful landscape', './generated-landscape.png');
```

```python [Python]
import requests
import os

def download_image(url, filename):
    response = requests.get(url)
    response.raise_for_status()
    
    with open(filename, 'wb') as f:
        f.write(response.content)
    
    return filename

def generate_and_save(prompt, filename):
    try:
        image_url = generate_image(prompt)
        saved_path = download_image(image_url, filename)
        print(f'Image saved to: {saved_path}')
        return saved_path
    except Exception as error:
        print(f'Error generating/saving image: {error}')
        raise

# Usage
generate_and_save('A beautiful landscape', './generated-landscape.png')
```

::

## Integration Examples

### React Component

```jsx
import React, { useState } from 'react';

const ImageGenerator = () => {
  const [prompt, setPrompt] = useState('');
  const [imageUrl, setImageUrl] = useState('');
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const generateImage = async () => {
    if (!prompt.trim()) return;
    
    setLoading(true);
    setError('');
    
    try {
      const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': 'Bearer YOUR_API_KEY'
        },
        body: JSON.stringify({
          prompt: prompt,
          model: 'imagen-v2',
          width: 1024,
          height: 1024
        })
      });

      if (!response.ok) {
        throw new Error(`Error: ${response.status}`);
      }

      const result = await response.json();
      setImageUrl(result.images[0].url);
    } catch (err) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="image-generator">
      <div className="input-section">
        <textarea
          value={prompt}
          onChange={(e) => setPrompt(e.target.value)}
          placeholder="Describe the image you want to generate..."
          rows={3}
        />
        <button 
          onClick={generateImage} 
          disabled={loading || !prompt.trim()}
        >
          {loading ? 'Generating...' : 'Generate Image'}
        </button>
      </div>

      {error && (
        <div className="error">
          Error: {error}
        </div>
      )}

      {imageUrl && (
        <div className="result">
          <img src={imageUrl} alt="Generated" />
          <a href={imageUrl} download="generated-image.png">
            Download Image
          </a>
        </div>
      )}
    </div>
  );
};

export default ImageGenerator;
```

### Express.js API Endpoint

```javascript
const express = require('express');
const app = express();

app.use(express.json());

app.post('/api/generate-image', async (req, res) => {
  try {
    const { prompt, model = 'imagen-v2', width = 1024, height = 1024 } = req.body;

    if (!prompt) {
      return res.status(400).json({ error: 'Prompt is required' });
    }

    const response = await fetch('https://api.imagen.ai/v1/text-to-image', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${process.env.IMAGEN_API_KEY}`
      },
      body: JSON.stringify({
        prompt,
        model,
        width,
        height
      })
    });

    if (!response.ok) {
      const error = await response.json();
      return res.status(response.status).json(error);
    }

    const result = await response.json();
    res.json(result);

  } catch (error) {
    console.error('Error generating image:', error);
    res.status(500).json({ error: 'Internal server error' });
  }
});

app.listen(3000, () => {
  console.log('Server running on port 3000');
});
```

## Best Practices

1. **Use environment variables** for API keys
2. **Implement proper error handling** with retry logic
3. **Cache generated images** to avoid regenerating the same content
4. **Use appropriate models** for your use case
5. **Handle rate limits** gracefully with exponential backoff
6. **Validate user inputs** before sending to the API
7. **Optimize for your specific use case** (batch processing, real-time generation, etc.)

## Next Steps

- [Text to Image API →](/api-reference/text-to-image)
- [Image to Image API →](/api-reference/image-to-image)
- [Available Models →](/api-reference/models)
