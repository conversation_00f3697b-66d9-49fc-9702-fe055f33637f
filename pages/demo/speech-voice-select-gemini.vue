<template>
  <div class="container mx-auto p-6 space-y-8">
    <div class="text-center">
      <h1 class="text-3xl font-bold mb-4">{{ $t('demo.speechVoiceSelectGemini.title') }}</h1>
      <p class="text-gray-600 dark:text-gray-400">
        {{ $t('demo.speechVoiceSelectGemini.description') }}
      </p>
      <div class="mt-4 space-y-3">
        <div class="p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
          <p class="text-sm text-blue-700 dark:text-blue-300">
            <strong>{{ $t('demo.speechVoiceSelectGemini.newFeature') }}:</strong>
            {{ $t('demo.speechVoiceSelectGemini.favoriteFeatureDescription') }}
          </p>
        </div>
        <div class="p-4 bg-green-50 dark:bg-green-900/20 rounded-lg">
          <p class="text-sm text-green-700 dark:text-green-300">
            <strong>{{ $t('demo.speechVoiceSelectGemini.flagFeature') }}:</strong>
            {{ $t('demo.speechVoiceSelectGemini.flagFeatureDescription') }}
          </p>
        </div>
      </div>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-2 gap-8">
      <!-- Regular Voice Select -->
      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold">{{ $t('demo.speechVoiceSelectGemini.allVoices') }}</h2>
        </template>

        <div class="space-y-4">
          <BaseSpeechVoiceSelectModal
            v-model="selectedVoice1"
            :placeholder="$t('demo.speechVoiceSelectGemini.selectAnyVoice')"
          />

          <div v-if="selectedVoice1" class="p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg">
            <h3 class="font-medium mb-2">{{ $t('demo.speechVoiceSelectGemini.selectedVoice') }}:</h3>
            <p><strong>{{ $t('demo.speechVoiceSelectGemini.name') }}:</strong> {{ selectedVoice1.speaker_name }}</p>
            <p><strong>{{ $t('demo.speechVoiceSelectGemini.type') }}:</strong> {{ selectedVoice1.type }}</p>
            <p><strong>{{ $t('demo.speechVoiceSelectGemini.voiceDescription') }}:</strong> {{ selectedVoice1.description }}</p>
          </div>
        </div>
      </UCard>

      <!-- Gemini Only Voice Select -->
      <UCard>
        <template #header>
          <h2 class="text-xl font-semibold">{{ $t('demo.speechVoiceSelectGemini.geminiOnly') }}</h2>
        </template>

        <div class="space-y-4">
          <BaseSpeechVoiceSelectModal
            v-model="selectedVoice2"
            :placeholder="$t('demo.speechVoiceSelectGemini.selectGeminiVoice')"
            :gemini-only="true"
          />

          <div v-if="selectedVoice2" class="p-4 bg-gray-50 dark:bg-neutral-800 rounded-lg">
            <h3 class="font-medium mb-2">{{ $t('demo.speechVoiceSelectGemini.selectedVoice') }}:</h3>
            <p><strong>{{ $t('demo.speechVoiceSelectGemini.name') }}:</strong> {{ selectedVoice2.speaker_name }}</p>
            <p><strong>{{ $t('demo.speechVoiceSelectGemini.type') }}:</strong> {{ selectedVoice2.type }}</p>
            <p><strong>{{ $t('demo.speechVoiceSelectGemini.voiceDescription') }}:</strong> {{ selectedVoice2.description }}</p>
          </div>
        </div>
      </UCard>
    </div>

    <!-- Code Examples -->
    <UCard>
      <template #header>
        <h2 class="text-xl font-semibold">{{ $t('demo.speechVoiceSelectGemini.codeExamples') }}</h2>
      </template>

      <div class="space-y-6">
        <div>
          <h3 class="font-medium mb-2">{{ $t('demo.speechVoiceSelectGemini.allVoicesExample') }}:</h3>
          <pre class="bg-gray-100 dark:bg-neutral-800 p-4 rounded-lg text-sm overflow-x-auto"><code>&lt;BaseSpeechVoiceSelectModal
  v-model="selectedVoice"
  placeholder="Select any voice"
/&gt;</code></pre>
        </div>

        <div>
          <h3 class="font-medium mb-2">{{ $t('demo.speechVoiceSelectGemini.geminiOnlyExample') }}:</h3>
          <pre class="bg-gray-100 dark:bg-neutral-800 p-4 rounded-lg text-sm overflow-x-auto"><code>&lt;BaseSpeechVoiceSelectModal
  v-model="selectedVoice"
  placeholder="Select Gemini voice"
  :gemini-only="true"
/&gt;</code></pre>
        </div>
      </div>
    </UCard>
  </div>
</template>

<script setup lang="ts">
import type { SpeechVoice } from '~/composables/useSpeechVoices'

// Page meta
definePageMeta({
  title: 'Speech Voice Select Gemini Demo',
  description: 'Demo of BaseSpeechVoiceSelectModal component with Gemini-only filtering'
})

// Reactive state for selected voices
const selectedVoice1 = ref<SpeechVoice | null>(null)
const selectedVoice2 = ref<SpeechVoice | null>(null)
</script>
