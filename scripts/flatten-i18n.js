#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

/**
 * Flatten nested object to dot notation
 * @param {Object} obj - Object to flatten
 * @param {string} prefix - Prefix for keys
 * @returns {Object} - Flattened object
 */
function flattenObject(obj, prefix = '') {
  const flattened = {};

  for (const key in obj) {
    if (obj.hasOwnProperty(key)) {
      const newKey = prefix ? `${prefix}.${key}` : key;

      if (typeof obj[key] === 'object' && obj[key] !== null && !Array.isArray(obj[key])) {
        // Recursively flatten nested objects
        Object.assign(flattened, flattenObject(obj[key], newKey));
      } else {
        // Add primitive values directly
        flattened[newKey] = obj[key];
      }
    }
  }

  return flattened;
}

/**
 * Process i18n locale file
 * @param {string} filePath - Path to the locale file
 */
function processLocaleFile(filePath) {
  try {
    console.log(`Processing: ${filePath}`);

    // Read the file
    const content = fs.readFileSync(filePath, 'utf8');
    const data = JSON.parse(content);

    // Flatten the object
    const flattened = flattenObject(data);

    // Write back to file with proper formatting
    const output = JSON.stringify(flattened, null, 2);
    fs.writeFileSync(filePath, output, 'utf8');

    console.log(`✅ Successfully flattened: ${filePath}`);
    console.log(`   Keys: ${Object.keys(flattened).length}`);

  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error.message);
  }
}

// Main execution
const localesDir = path.join(__dirname, '../i18n/locales');

// Get all JSON files in the locales directory
const files = fs.readdirSync(localesDir).filter(file => file.endsWith('.json'));

console.log(`Found ${files.length} locale files: ${files.join(', ')}`);

files.forEach(file => {
  const filePath = path.join(localesDir, file);
  if (fs.existsSync(filePath)) {
    processLocaleFile(filePath);
  } else {
    console.warn(`⚠️  File not found: ${filePath}`);
  }
});

console.log('\n🎉 Flattening complete!');
