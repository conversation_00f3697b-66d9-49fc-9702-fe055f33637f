# Test Speech Notifications Implementation

## Overview
This document outlines the testing steps for the speech generation loading states and notification handling implementation.

## Components Updated
1. `HistorySpeechCard.vue` - Added loading states and result display
2. `AIToolSpeechCard.vue` - Added loading and error states
3. `notifications.ts` - Added speech notification handling
4. `speech-gen.vue` - Added event listener for notifications
5. `en.json` - Added i18n keys

## Test Cases

### 1. Test Loading State in HistorySpeechCard
**Steps:**
1. Navigate to `/history` page
2. Find a speech item with `status: 1` (processing)
3. Verify loading spinner appears with "Generating speech..." message
4. Verify processing alert shows with "Go to History" button

**Expected Result:**
- Loading spinner with `svg-spinners:blocks-wave` icon
- Message: "Generating speech..."
- Processing alert with tip about background generation

### 2. Test Error State in HistorySpeechCard
**Steps:**
1. Navigate to `/history` page
2. Find a speech item with `status: 3` (error)
3. Verify error state appears with error icon and message

**Expected Result:**
- Error icon with red color
- "Generation failed" message
- Error details if available

### 3. Test Show/Hide Result Functionality
**Steps:**
1. Navigate to `/history` page
2. Find a speech item with `status: 2` (completed) and audio URL
3. Click "Show Result" button
4. Verify audio result appears with close button
5. Click close button to hide result

**Expected Result:**
- "Show Result" button appears for completed items
- Audio result shows with WaveformPlayer
- Close button (X) appears in result view
- Clicking close returns to normal view

### 4. Test AIToolSpeechCard Loading State
**Steps:**
1. Navigate to `/app/speech-gen` page
2. Generate a speech (should show loading immediately)
3. Verify loading state in AIToolSpeechCard

**Expected Result:**
- Loading spinner with "Generating speech..." message
- Processing alert appears when status is 1

### 5. Test Speech Notification Handling
**Steps:**
1. Navigate to `/app/speech-gen` page
2. Generate a speech
3. Simulate receiving a speech notification
4. Verify `textToSpeechResult` is updated

**Expected Result:**
- When on speech-gen page: result updates automatically
- When on other pages: notification drawer opens

### 6. Test I18n Keys
**Steps:**
1. Check all new i18n keys are working:
   - `ui.messages.speechGenerating`
   - `ui.labels.generatedAudio`
   - `ui.actions.showResult`
   - Speech processing message

**Expected Result:**
- All text displays correctly in English
- No missing translation warnings

## Manual Testing Commands

```bash
# Start the development server
npm run dev

# Navigate to different pages to test
# /history - Test HistorySpeechCard
# /app/speech-gen - Test AIToolSpeechCard and notifications
```

## Debugging Tips

1. **Check Console Logs:**
   - Look for notification handling logs in `handleNotificationSideEffects`
   - Check for speech notification events

2. **Check Network Tab:**
   - Verify API calls for speech generation
   - Check notification subscription

3. **Check Vue DevTools:**
   - Monitor store state changes
   - Check component props and state

## Known Issues to Watch For

1. **Event Listener Cleanup:**
   - Ensure event listeners are properly removed on unmount
   - Check for memory leaks

2. **Notification Timing:**
   - Verify notifications arrive at correct time
   - Check for duplicate notifications

3. **State Synchronization:**
   - Ensure store state updates correctly
   - Check for race conditions

## Success Criteria

✅ Loading states display correctly in both components
✅ Error states show appropriate messages
✅ Show/Hide result functionality works
✅ Notifications update speech results automatically
✅ Processing alerts guide users appropriately
✅ All i18n keys display correct text
✅ No console errors or warnings
✅ Event listeners are properly cleaned up
